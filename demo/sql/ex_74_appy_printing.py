"""printing/pdf - appy

You can print to a file in both .pdf and .odt format if you have uno
installed (it comes with openoffice) and can use an openoffice server.

NOTE:
This sample will *fail* if you cannot start an openoffice server
or if you dont have the 'uno' module from openoffice.

"""
from sqlkit.misc import printing
printing.set_printing_style('appy')

def debug_context(printtool, context, template_name, sqlwidget):
    print "CONTEXT:", context

t = SqlTable(model.Movie, dbproxy=db)
t.printing.add_menu_entry('Print to odt', 'movies-appy.odt', mode='odt',
                          tip="Generate a pdf file with these movies",
)
t.printing.add_menu_entry('Print to pdf', 'movies-appy.odt', mode='pdf',
                          accel="<alt>p",
                          tip="Generate a pdf file with these movies")
t.printing.connect('context-ready', debug_context)
t.reload()

## restore default for other examples
printing.set_printing_style('ooo')
