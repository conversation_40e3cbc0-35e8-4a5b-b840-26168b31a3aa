# Translations template for PROJECT.
# Copyright (C) 2011 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2011.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2011-03-03 12:33+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 0.9.4\n"

#: bin/sqledit.py:87
msgid "Connection setup"
msgstr ""

#: bin/sqledit.py:90
msgid ""
"\n"
"    You can indicate the database you want to connect to\n"
"    using an URI in the form as:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    or using password and username:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "
msgstr ""

#: bin/sqledit.py:117
msgid "Run Demo"
msgstr ""

#: bin/sqledit.py:140
msgid ""
"Info on available backend:\n"
"postgres, mysql..."
msgstr ""

#: bin/sqledit.py:141
msgid "Sqledit manual page"
msgstr ""

#: bin/sqledit.py:146
msgid ""
"Try continuosly to connect.\n"
"        Nice and usefull but may cause temporary blocks\n"
"        if you write an inexistent hostname\n"
"        "
msgstr ""

#: bin/sqledit.py:160
msgid ""
"If you write a wrong hostname\n"
"the application may hang some seconds till the natural network timeout. \n"
"Uncheck the flag on the right ti disable this feature"
msgstr ""

#: bin/sqledit.py:187
#, python-format
msgid "Attempting to connect to %s"
msgstr ""

#: bin/sqledit.py:190
#, python-format
msgid "Connected to %s"
msgstr ""

#: bin/sqledit.py:221
msgid "A complete demo of all the features of the sqlkit package"
msgstr ""

#: bin/sqledit.py:222
msgid ""
"The demo was not found, if you know where it is,\n"
"run it manually: python demo.py"
msgstr ""

#: sqlkit/exc.py:48
#, python-format
msgid "Field '%s' cannot be NULL"
msgstr ""

#: sqlkit/exc.py:60
#, python-format
msgid "Field '%s' cannot have value %s"
msgstr ""

#: sqlkit/fields.py:979
#, python-format
msgid "value is not Decimal nor string: %s"
msgstr ""

#: sqlkit/fields.py:1055
#, python-format
msgid "value is not date nor string: %s"
msgstr ""

#: sqlkit/fields.py:1174 sqlkit/widgets/mask/miniwidgets.py:430
#, python-format
msgid "Wrong date format: %s"
msgstr ""

#: sqlkit/fields.py:1184
msgid "Wrong time format"
msgstr ""

#: sqlkit/fields.py:1336
msgid "Value cannot be NULL"
msgstr ""

#: sqlkit/fields.py:1340
#, python-format
msgid "Value '%s' is not accepted"
msgstr ""

#: sqlkit/db/proxy.py:219
#, python-format
msgid "Table %s doesn't have a primary key, editing is not possible"
msgstr ""

#: sqlkit/layout/dateedit.py:190
msgid "Press Esc to close or double click a date to select it"
msgstr ""

#: sqlkit/layout/fk_entry.py:25
msgid "Find allowed values (Control-Enter/Shift-Enter)"
msgstr ""

#: sqlkit/layout/image_widget.py:142
msgid ""
"Right click on the image area\n"
" to upload an image"
msgstr ""

#: sqlkit/layout/image_widget.py:273 sqlkit/widgets/table/table.py:1395
msgid "Upload image"
msgstr ""

#: sqlkit/layout/image_widget.py:273
msgid "Upload or modify an image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete the image"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Open separate image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:276
msgid "Save image as"
msgstr ""

#: sqlkit/layout/image_widget.py:279
msgid "Show/Hide image name"
msgstr ""

#: sqlkit/layout/image_widget.py:317
msgid "upload image"
msgstr ""

#: sqlkit/misc/datetools.py:152
msgid "Incomplete date format"
msgstr ""

#: sqlkit/misc/table_browser.py:135
msgid "Database"
msgstr ""

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:136 sqlkit/misc/words.py:13
#: sqlkit/widgets/common/sqlwidget.py:795
msgid "Modify"
msgstr ""

#: sqlkit/misc/table_browser.py:137
msgid "Tool"
msgstr ""

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:138 sqlkit/misc/words.py:15
#: sqlkit/widgets/common/sqlwidget.py:798
msgid "Help"
msgstr ""

#: sqlkit/misc/table_browser.py:141
msgid "Count records"
msgstr ""

#: sqlkit/misc/table_browser.py:141
msgid "Count records in all tables"
msgstr ""

#: sqlkit/misc/table_browser.py:144
msgid ""
"Configure the fields: \n"
"labels, tooltip, completion, search field"
msgstr ""

#: sqlkit/misc/table_browser.py:147
msgid "Edit Sqlkit Fields"
msgstr ""

#: sqlkit/misc/table_browser.py:150
msgid "Primary Key"
msgstr ""

#: sqlkit/misc/table_browser.py:151
msgid "Show/Hide primary key if the key is numeric"
msgstr ""

#: sqlkit/misc/table_browser.py:152
msgid "Load data"
msgstr ""

#: sqlkit/misc/table_browser.py:153
msgid "Load the data as well"
msgstr ""

#: sqlkit/misc/table_browser.py:154
msgid "Blank"
msgstr ""

#: sqlkit/misc/table_browser.py:154
msgid "Cast blank into NULL"
msgstr ""

#: sqlkit/misc/table_browser.py:262
msgid "Mask"
msgstr ""

#: sqlkit/misc/table_browser.py:266
msgid "Table"
msgstr ""

#: sqlkit/misc/table_browser.py:272
msgid "Collapse row"
msgstr ""

#: sqlkit/misc/table_browser.py:276
msgid "Table reflection"
msgstr ""

#: sqlkit/misc/table_browser.py:423
msgid "The name of the table we are customizing"
msgstr ""

#: sqlkit/misc/table_browser.py:424
#, python-format
msgid ""
"The best representation of a record \n"
"as a combination of fields, e.g.: %(title)s %(year)s"
msgstr ""

#: sqlkit/misc/table_browser.py:427
msgid "The field that will be searched for when completion is used"
msgstr ""

#. TIP: filter page of the filter panel
#: sqlkit/misc/words.py:7
msgid "filter"
msgstr ""

#. TIP: output page of the filter panel
#: sqlkit/misc/words.py:9 sqlkit/widgets/common/sqlfilter.py:259
#: sqlkit/widgets/common/sqlfilter.py:306
msgid "output"
msgstr ""

#. TIP: menu entry
#: sqlkit/misc/words.py:11
msgid "File"
msgstr ""

#. TIP: menu entry
#: sqlkit/misc/words.py:17 sqlkit/widgets/common/sqlwidget.py:796
msgid "Go"
msgstr ""

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:19
msgid "opts"
msgstr ""

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:21 sqlkit/widgets/common/sqlwidget.py:797
msgid "Tools"
msgstr ""

#. TIP: serach mode:
#: sqlkit/misc/words.py:23
msgid "regexp"
msgstr ""

#. TIP: search mode
#: sqlkit/misc/words.py:25
msgid "start"
msgstr ""

#. TIP: count records in sqledit
#: sqlkit/misc/words.py:27
msgid "N.Rows"
msgstr ""

#: sqlkit/misc/words.py:29
msgid "Indexes"
msgstr ""

#: sqlkit/misc/words.py:30
msgid "Nullable"
msgstr ""

#: sqlkit/misc/words.py:31
msgid "Prim. Keys"
msgstr ""

#: sqlkit/misc/words.py:32
msgid "Default"
msgstr ""

#: sqlkit/misc/words.py:33
msgid "Foreign Key"
msgstr ""

#: sqlkit/misc/words.py:34
msgid "filter_tables"
msgstr ""

#. TIP: status bar
#: sqlkit/widgets/common/completion.py:285
#: sqlkit/widgets/common/completion.py:1124
#, python-format
msgid "search mode: %s"
msgstr ""

#: sqlkit/widgets/common/completion.py:643
msgid "Show all info on this field, db type & Co."
msgstr ""

#. TIP: menu enty in menu on right click on down arro in fkey completion widget
#: sqlkit/widgets/common/completion.py:649
msgid "Show possible values: regexp  - Ctrl-Enter"
msgstr ""

#. TIP: yellow tip to menu entry in down arrow in completion widget
#: sqlkit/widgets/common/completion.py:653
msgid "Show all values in the db that match your string"
msgstr ""

#: sqlkit/widgets/common/completion.py:658
msgid "Show possible values, starting - Shift-Enter"
msgstr ""

#: sqlkit/widgets/common/completion.py:661
msgid "Show all values in the database starting with your string"
msgstr ""

#: sqlkit/widgets/common/completion.py:667
msgid "Edit the referenced table in 'table' mode"
msgstr ""

#: sqlkit/widgets/common/completion.py:669
msgid "Edit the table from which admitted values are taken in 'table' mode"
msgstr ""

#: sqlkit/widgets/common/completion.py:674
msgid "Edit the referenced table in 'mask' mode"
msgstr ""

#: sqlkit/widgets/common/completion.py:676
msgid "Edit the table from which admitted values are taken in 'mask' mode"
msgstr ""

#: sqlkit/widgets/common/completion.py:783
msgid "Good match"
msgstr ""

#: sqlkit/widgets/common/completion.py:792
#: sqlkit/widgets/common/completion.py:798
msgid "No exact match, trying regexp completion"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:26 sqlkit/widgets/common/dialogs.py:94
#: sqlkit/widgets/table/table.py:1703
msgid "All files"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:100
msgid "Images"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:106
msgid "Documents"
msgstr ""

#. TIP: possible new name for an uploaded file
#: sqlkit/widgets/common/dialogs.py:123
msgid "Preferred new filename:"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:124
msgid "If not empty, the file will be uploaded with this new name"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:107
#: sqlkit/widgets/common/sqlfilter.py:110
msgid "Match as regexp"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:108
msgid "Match as LIKE, \"%\" automatically added"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:109
msgid "Match as LIKE case insensitive, \"%\" automatically added"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:111
msgid "Match as regexp, case insensitive"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:112
msgid "Negation of match as regexp"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:113
msgid "Negation of match case insensitive"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:114
msgid "Equal"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:115
msgid "Not equal"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:116
msgid "Greater then (after than)"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:117
msgid "Greater or equal"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:118
msgid "Less than (before then)"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:119
msgid "Less than or equal"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:120
msgid "LIKE: a \"%\" means any char - case sensitive"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:121
msgid "Negation of LIKE"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:122
msgid "As LIKE but case insensitive"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:123
msgid "Negation of ILIKE"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:124
msgid "The boolean is True"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:125
msgid "The boolean is False"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:126
msgid "The boolean is not True"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:127
msgid "The boolean is not False"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:128
msgid "The value is not set"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:129
msgid "The value is set"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:130
msgid "ID equality (don't follow foreign table)"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:221
msgid "Filter Panel"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:230
msgid "Add filters for your query"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:232
msgid "Result page for your query"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:269
msgid "Filter actions"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:270
msgid "Reload from db"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:271
msgid "Close the panel"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:274
msgid "Go to filter panel"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:276
msgid "Go to output panel"
msgstr ""

#. TIP: status bar message
#: sqlkit/widgets/common/sqlfilter.py:479 sqlkit/widgets/table/table.py:762
#: sqlkit/widgets/table/table.py:1039
#, python-format
msgid "Total N. of records: %s"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:801
#, python-format
msgid "value '%s' cannot be used for field '%s'"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:868
#, python-format
msgid ""
"value '%s' does not seem a valid date and cannot be transformed into a "
"date"
msgstr ""

#. TIP: appears in the menu in the filter panel to add a second entry of the
#. same field
#: sqlkit/widgets/common/sqlfilter.py:1042
#, python-format
msgid "Add a new filter on this field '%s'"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:1048
msgid "Use this filter"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:1092
msgid "Click here to select an operator"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:1164
msgid "incomplete date format"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:358
#, python-format
msgid ""
"Sorry, problems connecting to remote db. Original error was: \n"
"\n"
"%s"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:625
msgid "Hiding field is only supported for Tables"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:792
msgid "Show all the differences that should be saved to database"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:802
msgid "Pending Differences"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:806
msgid "Save current record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Filter panel"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Add filter panel"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload from the database"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:815
msgid "Go to next record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:816
msgid "Go to previous record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:823
msgid "Inspect widgets"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:990
msgid "Saved"
msgstr ""

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1020
#, python-format
msgid ""
"Error while writing to the database. \n"
"Original error was: \n"
"%s"
msgstr ""

#. TIP: Error while saving into a table w/o permission
#. TIP: reloading data from the database
#: sqlkit/widgets/common/sqlwidget.py:1026
#: sqlkit/widgets/common/sqlwidget.py:1563
#, python-format
msgid ""
"A programming error was received from the backend:\n"
"%s"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1035
#, python-format
msgid "%s - Dirty objects: %s - New objects: %s"
msgstr ""

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1041
#, python-format
msgid ""
"Error while writing to the database: \n"
"are you 'saving as new' an already known record?\n"
"original error was: \n"
"%s"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1186
msgid "Discarding new obj"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1223
#, python-format
msgid "Char %s is not accepted in numeric context"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1281
msgid "record validation"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1503
msgid "Unsaved differences"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1741
msgid "Sorry, export is implemented only for table view"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2097
msgid "Property"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2098
msgid "Value"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2184
msgid "Save unsaved data?"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2187
msgid "Unsaved data"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2209
msgid "State"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2210
#: sqlkit/widgets/common/sqlwidget.py:2330
msgid "Field name"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2211
msgid "Original value"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2212
msgid "Present value"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2242
msgid "Modified"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2269 sqlkit/widgets/mask/mask.py:597
msgid "Deleted"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2279
msgid "New"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2293
msgid "empty string: ''"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2295
msgid "NULL value"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2301
msgid ""
"Errors are present in the record. \n"
"Correct them now, to continue \n"
"or delete the record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2302
msgid "Validation errors"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2331
msgid "Error"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2362
msgid ""
"You can continue or go back editing. \n"
"Read the following warnings to decide"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2363
msgid "Validation Warnings"
msgstr ""

#: sqlkit/widgets/mask/mask.py:258
msgid "Add new record"
msgstr ""

#: sqlkit/widgets/mask/mask.py:262
msgid "Discard changes"
msgstr ""

#. TIP Modify menu entry in the mask to reread a single record from database
#: sqlkit/widgets/mask/mask.py:264
msgid "Refresh this record"
msgstr ""

#: sqlkit/widgets/mask/mask.py:265
msgid "Reread this record from db"
msgstr ""

#: sqlkit/widgets/mask/mask.py:268
msgid "Delete this record"
msgstr ""

#: sqlkit/widgets/mask/mask.py:342 sqlkit/widgets/table/table.py:908
msgid "Already at new record"
msgstr ""

#: sqlkit/widgets/mask/mask.py:362
#, python-format
msgid "New record %s"
msgstr ""

#. TIP message issued when a refresh is done on a deleted record
#: sqlkit/widgets/mask/mask.py:405
msgid "The record is no longer present in the database"
msgstr ""

#: sqlkit/widgets/mask/mask.py:473 sqlkit/widgets/table/table.py:838
msgid "Nothing to save"
msgstr ""

#: sqlkit/widgets/mask/mask.py:523
#, python-format
msgid "Primary key (%s) didn't change. Refusing to save as new"
msgstr ""

#: sqlkit/widgets/mask/mask.py:537
msgid "Do you want to copy all data to a new record?"
msgstr ""

#: sqlkit/widgets/mask/mask.py:585 sqlkit/widgets/table/table.py:982
#, python-format
msgid ""
"Delete this record?\n"
"(%s)"
msgstr ""

#: sqlkit/widgets/mask/mask.py:654
msgid "No record present"
msgstr ""

#: sqlkit/widgets/mask/mask.py:677
msgid "Already last record"
msgstr ""

#: sqlkit/widgets/mask/mask.py:679
msgid "Already first record"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:695
#, python-format
msgid "A file with name '%s' already exists. Overwrite?"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:696
msgid "Upload name conflict"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:888
#, python-format
msgid "'%s' may have an invalid value: try completion on that"
msgstr ""

#: sqlkit/widgets/table/columns.py:208
msgid "Editing canceled. Restoring original value"
msgstr ""

#: sqlkit/widgets/table/columns.py:848
#, python-format
msgid "Add a filter on '%s'"
msgstr ""

#: sqlkit/widgets/table/columns.py:858
msgid ""
"Sort on this column reloading from the database, you can used 's' to sort"
" locally"
msgstr ""

#: sqlkit/widgets/table/columns.py:861
msgid "Sort on this column locally (w/o touching the database)"
msgstr ""

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:871
msgid "Hide this column"
msgstr ""

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:878
msgid "Create total"
msgstr ""

#. TIP: column menu total
#: sqlkit/widgets/table/columns.py:888
#, python-format
msgid "Subtotal on %s"
msgstr ""

#. TIP: column menu opt
#. TIP: modify menu entry
#: sqlkit/widgets/table/columns.py:910 sqlkit/widgets/table/columns.py:1293
#: sqlkit/widgets/table/table.py:287
msgid "Show field"
msgstr ""

#: sqlkit/widgets/table/columns.py:911 sqlkit/widgets/table/columns.py:1294
#: sqlkit/widgets/table/table.py:288
msgid "Hide field"
msgstr ""

#: sqlkit/widgets/table/columns.py:938
msgid "day"
msgstr ""

#: sqlkit/widgets/table/columns.py:938
msgid "week"
msgstr ""

#: sqlkit/widgets/table/columns.py:938
msgid "month"
msgstr ""

#: sqlkit/widgets/table/columns.py:939
msgid "quarter"
msgstr ""

#: sqlkit/widgets/table/columns.py:939
msgid "year"
msgstr ""

#: sqlkit/widgets/table/columns.py:942
#, python-format
msgid "Subtotals by %s"
msgstr ""

#: sqlkit/widgets/table/columns.py:1267
msgid "Right click on this table to show the column again"
msgstr ""

#: sqlkit/widgets/table/columns.py:1287 sqlkit/widgets/table/table.py:301
msgid "Export"
msgstr ""

#: sqlkit/widgets/table/columns.py:1287
msgid "Export these data into csv format"
msgstr ""

#: sqlkit/widgets/table/columns.py:1292
msgid "Adapt width of columns to data"
msgstr ""

#: sqlkit/widgets/table/columns.py:1321
#, python-format
msgid "View '%s' in a Mask"
msgstr ""

#: sqlkit/widgets/table/table.py:274
msgid "Duplicate"
msgstr ""

#: sqlkit/widgets/table/table.py:275
msgid "Create a new row as a duplicate of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:277
msgid "New child row"
msgstr ""

#: sqlkit/widgets/table/table.py:278
msgid "Create e new row as child of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:292
msgid "View this record in a Mask"
msgstr ""

#: sqlkit/widgets/table/table.py:293
msgid "View this ForeignKey in a Mask"
msgstr ""

#: sqlkit/widgets/table/table.py:294
msgid "Upload Image"
msgstr ""

#: sqlkit/widgets/table/table.py:295
msgid "Upload File"
msgstr ""

#: sqlkit/widgets/table/table.py:296
msgid "Delete File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:297
msgid "Show File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save file locally"
msgstr ""

#: sqlkit/widgets/table/table.py:528 sqlkit/widgets/table/table.py:542
#: sqlkit/widgets/table/table.py:554
msgid "Insertion of new records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:558
msgid "Update of records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:570
msgid "Deletion of records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:583
msgid "browsing of new records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:731
msgid "no current obj: maybe no record has yet been edited"
msgstr ""

#. TIP: when saving m2m, we delay till leader record will be saved
#: sqlkit/widgets/table/table.py:869
msgid "delaying saving when main record will be saved"
msgstr ""

#: sqlkit/widgets/table/table.py:879
msgid "Record has NOT been saved"
msgstr ""

#: sqlkit/widgets/table/table.py:978
msgid "No record selected"
msgstr ""

#: sqlkit/widgets/table/table.py:1137
msgid "Value is not valid, trying completion"
msgstr ""

#. TIP: check in input field length %s %s -> field_name length
#: sqlkit/widgets/table/table.py:1170
#, python-format
msgid "Input exceeded max %s length (%s)"
msgstr ""

#: sqlkit/widgets/table/table.py:1284
msgid "Unsaved data prevent opening a Mask to show the (unsaved) record"
msgstr ""

#: sqlkit/widgets/table/table.py:1407
#, python-format
msgid ""
"A file with name '%s' already exists.\n"
"Overwrite?"
msgstr ""

#: sqlkit/widgets/table/table.py:1408
msgid "Upload name duplication"
msgstr ""

#: sqlkit/widgets/table/table.py:1629
msgid "Export in csv file"
msgstr ""

#: sqlkit/widgets/table/table.py:1702
msgid "Csv files"
msgstr ""

#: sqlkit/widgets/table/tablewidgets.py:34
#, python-format
msgid "No current obj for field '%s' in %s"
msgstr ""

