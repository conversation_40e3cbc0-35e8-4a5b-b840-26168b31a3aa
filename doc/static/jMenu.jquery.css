/* .horizontal_menu { */
/*     height: 20px; */
/* } */
#jMenu {
    /* display: table; */
    margin: 0;
    font-weight: bold
    }
/********************/
/** premier niveau **/
/********************/
#jMenu li {
    display: table-cell;
    /* background-color: #f89c22; */
    /* margin: 0 */
    padding-right: 7px
    }
#jMenu li a {
    padding: 10px;
    background-color: #da812d;
    display: block;
    -moz-border-radius-topright: 6px;
    /* background-color: transparent; */
    color: #fff;
    cursor: pointer;
    font-size: 13px
    }
#jMenu li a:hover {
    background-color: #f8d322;
    color: #a91819
    }
/*******************/
/** second niveau **/
/*******************/
#jMenu li:hover, #jMenu li ul li:hover {
    color: #a91819;
    -moz-border-radius-topright: 6px;
    text-decoration: none
    }
#jMenu li ul {
    display: none;
    position: absolute;
    padding: 0;
    margin: 0
    }
#jMenu li ul li {
    background-color: #f8d322;
    -moz-border-radius-topright: 6px;
    display: block;
    border-bottom: 1px solid #da812d;
    padding: 0
    }
#jMenu li ul li.arrow {
    background: #f5f053 url(../arrow_down.png) no-repeat center center;
    color: #f00;
    height: 6px;
    padding: 0;
    border-bottom: none;
    padding-bottom: 10px
    }
#jMenu li ul li a {
    font-size: 11px;
    text-transform: none;
    background-color: #f8d322;
    color: #a91819;
    padding: 7px;
    display: block;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent
    }
#jMenu li ul li a.isParent {
    background: #f8d322 url(../arrow_right.png) no-repeat right center;
    color: #a91819
    }
#jMenu li ul li a:hover {
    background-color: #f5f053;
    -moz-border-radius-topright: 6px;
    color: #a91819;
    border-top: 1px solid #322f32;
    border-bottom: 1px solid #322f32
    }
