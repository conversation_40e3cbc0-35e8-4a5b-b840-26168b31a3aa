# -*- mode: python -*-

>>> import sys
>>> sys.path.insert(0, '../../demo/sql/model')
>>> sys.path.insert(0, '../../')

>>> from movies_autoload import Movie, Director, db
>>> from sqlkit import SqlMask
>>> from sqlkit.widgets.common.fields import FieldChooser

>>> lay = "title year director_id m2m=genres date_release"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> field_chooser = FieldChooser(sm, sm.widgets)
>>> field_name, key = 'title', 'title'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key)
<VarcharField - title >

>>> field_name, key = 'year', 'year'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key)
<IntegerField - year >

>>> field_name, key = 'director_id', 'director_id'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key)
<ForeignKeyField - director_id >

>>> field_name, key = 'director_id', 'fk=director_id'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key)
<ForeignKeyField - director_id >

>>> field_name, key = 'genres', 'm2m=genres'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key, test=True)
<CollectionField - genres >

>>> field_name, key = 'date_release', 'date_release'
>>> print field_chooser.get_field(field_name, sm.mapper_info.fields[field_name], key)
<DateField - date_release >

