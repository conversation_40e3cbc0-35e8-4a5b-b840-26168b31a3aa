/* AnythingSlider v1.41 cs-portfolio
   By <PERSON> (http://www.curtisscott.com/portfolio.html)
   updated to work with the new themes
*/

/*** Note: the nav-cs-portfolio.png used for navigation and slideshow buttons uses a semi-transparent png,
     through which the background color is seen... so colors set will turn out darker than normal ***/

/****** SET COLORS HERE *******/
/* Default/Acitve State */
div.anythingSlider-cs-portfolio .thumbNav a, div.anythingSlider-cs-portfolio.activeSlider .thumbNav a {
	background-color: #fff;
}
div.anythingSlider-cs-portfolio .thumbNav a:hover, div.anythingSlider-cs-portfolio .thumbNav a.cur,
div.anythingSlider-cs-portfolio.activeSlider .thumbNav a:hover, div.anythingSlider-cs-portfolio.activeSlider .thumbNav a.cur {
	background-color: #0d5c9f;
}
div.anythingSlider-cs-portfolio .start-stop, div.anythingSlider-cs-portfolio .start-stop.hover {
	background-color: #080;
}
div.anythingSlider-cs-portfolio .start-stop:hover {
	background-color: #0f0;
}
div.anythingSlider-cs-portfolio .start-stop.playing {
	background-color: #a00;
}
div.anythingSlider-cs-portfolio .start-stop.playing:hover {
	background-color: #f00;
}

/* Overall */
div.anythingSlider-cs-portfolio {
	/*margin-bottom: 50px;  add 50px to fit bar below the slider */
}
div.anythingSlider-cs-portfolio .anythingWindow, div.anythingSlider-cs-portfolio.activeSlider .anythingWindow {
	border: 0;
}
/* Navigation Arrows */
div.anythingSlider-cs-portfolio .arrow {
	display: block;
	bottom: -43px;
	position: absolute;
	z-index: 100;
}
div.anythingSlider-cs-portfolio .arrow a {
	display: block;
	bottom: 50px;
	position: absolute;
	height: 35px;
	width: 35px;
	outline: 0;
	background: url(../images/arrows-cs-portfolio.jpg) no-repeat;
	text-indent: -9999px;
}
div.anythingSlider-cs-portfolio .forward { right: 70px; }
div.anythingSlider-cs-portfolio .back { left: 50px; }
div.anythingSlider-cs-portfolio .forward a { background-position: left top; }
div.anythingSlider-cs-portfolio .back a { background-position: right top; }
div.anythingSlider-cs-portfolio .forward a:hover, div.anythingSlider-cs-portfolio .forward a.hover { background-position: left bottom; }
div.anythingSlider-cs-portfolio .back a:hover, div.anythingSlider-cs-portfolio .back a.hover { background-position: right bottom; }

/* Navigation Links */
div.anythingSlider-cs-portfolio .anythingControls {
	background: url(../images/bg-cs-portfolio.jpg) repeat-x bottom center;
	height: 50px;
	margin: 0 auto 50px auto;
	text-align: center;
	z-index: 100;
}
div.anythingSlider-cs-portfolio .thumbNav {
	float: none;
	margin: 0;
	z-index: 100;
}
div.anythingSlider-cs-portfolio .thumbNav li {
	display: inline;
}

div.anythingSlider-cs-portfolio .thumbNav a, div.anythingSlider-cs-portfolio.activeSlider .thumbNav a {
	display: inline-block;
	width: 18px;
	height: 15px;
	margin: 20px 5px 0 0;
	padding: 0;
	text-indent: -9999px;
	outline: 0;
	border: 0;
}

div.anythingSlider-cs-portfolio .thumbNav a, div.anythingSlider-cs-portfolio.activeSlider .thumbNav a,
div.anythingSlider-cs-portfolio .thumbNav a:hover, div.anythingSlider-cs-portfolio .thumbNav a.cur,
div.anythingSlider-cs-portfolio.activeSlider .thumbNav a:hover, div.anythingSlider-cs-portfolio.activeSlider .thumbNav a.cur {
	background-image: url(../images/nav-cs-portfolio.png);
	background-position: center top;
	background-repeat: no-repeat;
}

/* slider autoplay right-to-left, reverse order of nav links to look better */
div.anythingSlider-cs-portfolio.rtl .thumbNav a { float: right; } /* reverse order of nav links */
div.anythingSlider-cs-portfolio.rtl .thumbNav { float: left; }    /* move nav link group to left */
/* div.anythingSlider-cs-portfolio.rtl .start-stop { float: right; } */ /* move start/stop button - in case you want to switch sides */

/* Autoplay Start/Stop button */
div.anythingSlider-cs-portfolio .start-stop, div.anythingSlider-cs-portfolio .start-stop.hover {
	margin: 18px 50px 0 0;
	padding: 0;
	display: inline-block;
	text-align: center;
	width: 18px;
	height: 18px;
	z-index: 100;
	text-indent: -9999px;
	border: 0;
}
div.anythingSlider-cs-portfolio .start-stop, div.anythingSlider-cs-portfolio .start-stop.hover,
div.anythingSlider-cs-portfolio .start-stop:hover, div.anythingSlider-cs-portfolio .start-stop.playing,
div.anythingSlider-cs-portfolio .start-stop.playing:hover {
	background-image: url(../images/nav-cs-portfolio.png);
	background-position: center bottom;
	background-repeat: no-repeat;
}

/* Extra - replace defaults */
div.anythingSlider-cs-portfolio {
    /* padding: 0 23px 50px 0; */
    padding: 25px 30px 90px 30px;
    background-color: #444;
}
div.anythingSlider-cs-portfolio pre {
    padding: 0;
    background-color: #444;
    border: 0;
}
div.anythingSlider-cs-portfolio h2 {
    color: #eee;
}
div.anythingSlider-cs-portfolio tt {
    color: #eee;
}
