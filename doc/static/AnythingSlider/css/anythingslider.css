/*
    AnythingSlider v1.4.1+ Default (base) theme

    By <PERSON>: http://css-tricks.com
    with major improvements by <PERSON>: http://pixelgraphics.us/
    based on work by <PERSON><PERSON>: http://jqueryfordesigners.com/
*/

/******* SET DEFAULT DIMENSIONS HERE ********/
div.anythingSlider {
	width: 700px;
	height: 390px;
	margin: 0 auto;
	overflow: hidden; /* needed for Opera and Safari */
}

/****** SET COLORS HERE *******/
/* Default State */
div.anythingSlider .thumbNav a.cur, div.anythingSlider .thumbNav a {
	background: #777;
	color: #000;
}
div.anythingSlider .anythingWindow {
	border-top: 3px solid #777;
	border-bottom: 3px solid #777;
}
div.anythingSlider .start-stop {
	background-color: #040;
	color: #fff;
}
div.anythingSlider .start-stop.playing {
	background-color: #800;
}
div.anythingSlider .start-stop:hover, div.anythingSlider .start-stop.hover {
	color: #ddd;
}

/* Active State */
div.anythingSlider.activeSlider .anythingWindow {
	border-color: #7C9127;
}
div.anythingSlider.activeSlider .thumbNav a.cur, div.anythingSlider.activeSlider .thumbNav a {
	background-color: #7C9127;
}
div.anythingSlider .start-stop {
	background-color: #080;
	color: #fff;
}
div.anythingSlider .start-stop.playing {
	background-color: #d00;
}
div.anythingSlider .start-stop:hover, div.anythingSlider .start-stop.hover {
	color: #fff;
}

/**** DO NOT CHANGE BELOW THIS LINE ****/
/* anythingSlider viewport window */
div.anythingSlider .anythingWindow {
	overflow: hidden;
	position: relative;
	width: 100%;
	height: 100%;
}
/* wrapper: 45px right & left padding for the arrows, 28px @ bottom for navigation */
div.anythingSlider {
	position: relative;
	padding: 0 45px 28px 45px;
}
/* anythingSlider base UL */
ul.anythingBase {
	background: transparent;
	list-style: none;
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	padding: 0;
}
ul.anythingBase li.panel {
	background: transparent;
	display: block;
	overflow: hidden;
	float: left;
	padding: 0;
	margin: 0;
}

/* Navigation Arrows */
div.anythingSlider .arrow {
	top: 50%;
	position: absolute;
	display: block;
}
div.anythingSlider .arrow a {
	display: block;
	height: 120px;
	margin: -60px 0 0 0;
	width: 45px;
	text-align: center;
	outline: 0;
	background: url(../images/arrows-default.png) no-repeat;
	text-indent: -9999px;
}
div.anythingSlider .forward { right: 0; }
div.anythingSlider .back { left: 0; }
div.anythingSlider .forward a { background-position: 0 -40px; }
div.anythingSlider .back a { background-position: -88px -40px; }
div.anythingSlider .forward a:hover, div.anythingSlider .forward a.hover { background-position: 0 -240px; }
div.anythingSlider .back a:hover, div.anythingSlider .back a.hover { background-position: -88px -240px; }
div.anythingSlider .forward.disabled { display: none; } /* disabled arrows, hide or reduce opacity: opacity: .5; filter: alpha(opacity=50); */
div.anythingSlider .back.disabled { display: none; }

/* Navigation Links */
div.anythingSlider .anythingControls { outline: 0; }
div.anythingSlider .thumbNav { margin: 0; }
div.anythingSlider .thumbNav li { display: inline; }
div.anythingSlider .thumbNav a {
	font: 11px/18px Georgia, Serif;
	display: inline-block;
	text-decoration: none;
	padding: 2px 8px;
	height: 18px;
	margin: 0 5px 0 0;
	background-image: url(../images/cellshade.png);
	background-repeat: repeat-x;
	text-align: center;
	outline: 0;
	border-radius: 0 0 5px 5px;
	-moz-border-radius: 0 0 5px 5px;
	-webkit-border-radius: 0 0 5px 5px;
}
div.anythingSlider .thumbNav a:hover {
	background-image: none;
}

/* slider autoplay right-to-left, reverse order of nav links to look better */
div.anythingSlider.rtl .thumbNav a { float: right; } /* reverse order of nav links */
div.anythingSlider.rtl .thumbNav { float: left; }    /* move nav link group to left */
div.anythingSlider.rtl .anythingWindow {
	direction: ltr;
	unicode-bidi: bidi-override;
}
/* div.anythingSlider.rtl .start-stop { float: right; } */ /* move start/stop button - in case you want to switch sides */

/* Autoplay Start/Stop button */
div.anythingSlider .start-stop {
	background-image: url(../images/cellshade.png);
	background-repeat: repeat-x;
	background-position: center top;
	padding: 2px 5px;
	width: 40px;
	text-align: center;
	text-decoration: none;
	float: right;
	z-index: 100;
	outline: 0;
	border-radius: 0 0 5px 5px;
	-moz-border-radius: 0 0 5px 5px;
	-webkit-border-radius: 0 0 5px 5px;
}
div.anythingSlider .start-stop:hover, div.anythingSlider .start-stop.hover { background-image: none; }

div.anythingSlider, div.anythingSlider .anythingWindow, div.anythingSlider .thumbNav a, div.anythingSlider .arrow a, div.anythingSlider .start-stop {
	transition-duration: 0;
	-o-transition-duration: 0;
	-moz-transition-duration: 0;
	-webkit-transition-duration: 0;
}