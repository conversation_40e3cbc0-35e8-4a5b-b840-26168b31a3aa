
Sqlkit has been written by <PERSON><PERSON> <<EMAIL>>


In the late '90 I developed -as my first job with GUI- tksql a 
library in tcl/tk that is still being used in a cpuple of production
environment. 

Sqlkit is meant as a replacement, better under all possible points of view:
better language, nicer grafical toolkit, based on a serious ORM.

Contributors
=============

The first version of SqlTable was started by <PERSON> 
The module optionparse was written by <PERSON> 
The module dateedit was written by <PERSON>
The multiline renderer was set to pygtk list by <PERSON><PERSON>
The way tables width are worked out has been developed by <PERSON>
