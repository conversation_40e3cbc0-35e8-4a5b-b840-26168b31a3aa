{%- macro sidebar %}
      {%- if builder != 'htmlhelp' %}
      <div class="sidebar_sphinx">
        <div class="sidebarwrapper">
          {% if display_toc %}
            <h3>Contentenuti</h3>
            {{ toc }}
          {% endif %}
          {%- if prev %}
            <h4>Argomento precedente</h4>
            <p class="topless"><a href="{{ prev.link|e }}" title="previous chapter">{{ prev.title }}</a></p>
          {%- endif %}
          {%- if next %}
            <h4>Argomento successivo</h4>
            <p class="topless"><a href="{{ next.link|e }}" title="next chapter">{{ next.title }}</a></p>
          {%- endif %}
          {% if sourcename %}
            <h3>Questa Pagina</h3>
            <ul class="this-page-menu">
            {% if builder == 'web' %}
              <li><a href="#comments">Commenti ({{ comments|length }} so far)</a></li>
              <li><a href="{{ pathto('@edit/' + sourcename)|e }}">Suggest Change</a></li>
              <li><a href="{{ pathto('@source/' + sourcename)|e }}">Sorgente della pagina</a></li>
      
            {% elif builder == 'html' %}
              <li><a href="{{ pathto('_sources/' + sourcename, true)|e
              }}">Mostra Sorgente</a></li>
            {% endif %}
            </ul>
          {% endif %}
          {% if customsidebar %}
          {{ rendertemplate(customsidebar) }}
          {% endif %}
          {% if current_page_name != "search" %}
            <h3>{{ builder == 'web' and 'Keyword' or 'Quick' }} search</h3>
            <form class="search" action="{{ pathto('search') }}" method="get">
              <input type="text" name="q" size="18"> <input type="submit" value="Go">
              <input type="hidden" name="check_keywords" value="yes">
              <input type="hidden" name="area" value="default">
            </form>
            {% if builder == 'web' %}
            <p style="font-size: 90%">Enter a module, class or function name.</p>
            {% endif %}
          {% endif %}
        </div>
      </div>
      {%- endif %}
{%- endmacro %}

