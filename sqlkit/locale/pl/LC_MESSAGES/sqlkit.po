# Polish translations for PROJECT.
# Copyright (C) 2010 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2010-05-20 14:11+0200\n"
"PO-Revision-Date: 2011-03-13 00:08+0100\n"
"Last-Translator: <PERSON> <se<PERSON><PERSON><PERSON><PERSON><PERSON>@tlen.pl>\n"
"Language-Team: pl <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && "
"(n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 0.9.4\n"

#: bin/sqledit.py:87
msgid "Connection setup"
msgstr "Parametry połączenia"

#: bin/sqledit.py:90
msgid ""
"\n"
"    You can indicate the database you want to connect to\n"
"    using an URI in the form as:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    or using password and username:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "
msgstr ""
"\n"
"    Możesz wskazać bazę danych, z którą chcesz nawiązać połączenie\n"
"    używając następujących form adresu:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    lub podając nazwę użytkownika i hasło:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "

#: bin/sqledit.py:117
msgid "Run Demo"
msgstr "Włącz demonstrację"

#: bin/sqledit.py:140
msgid ""
"Info on available backend:\n"
"postgres, mysql..."
msgstr ""
"Informacja o dostępności silników bazodanowych:\n"
"postgres, mysql"

#: bin/sqledit.py:141
msgid "Sqledit manual page"
msgstr "Przewodnik po Sqledit"

#: bin/sqledit.py:146
msgid ""
"Try continuosly to connect.\n"
"        Nice and usefull but may cause temporary blocks\n"
"        if you write an inexistent hostname\n"
"        "
msgstr ""
"Nie przestawaj nawiązywać połączenia\n"
"        Jest to użyteczne, lecz może powodować chwilowe przestoje,\n"
"        jeśli określisz nieistniejący adres serwera.\n"

#: bin/sqledit.py:160
msgid ""
"If you write a wrong hostname\n"
"the application may hang some seconds till the natural network timeout. \n"
"Uncheck the flag on the right ti disable this feature"
msgstr ""
"Jeśli użyjesz nieprawidłowego adresu\n"
"aplikacja może zawiesić się na krótki czas poszukiwań w sieci (timeout)."
" \n"
"Odznacz flagę po prawej by zablokować tę funkcję"

#: bin/sqledit.py:187
#, python-format
msgid "Attempting to connect to %s"
msgstr "Trwa próba połączenia do %s"

#: bin/sqledit.py:190
#, python-format
msgid "Connected to %s"
msgstr "Połączono z %s"

#: bin/sqledit.py:221
msgid "A complete demo of all the features of the sqlkit package"
msgstr "Pełna demonstracja możliwości pakietu sqlkit"

#: bin/sqledit.py:222
msgid ""
"The demo was not found, if you know where it is,\n"
"run it manually: python demo.py"
msgstr ""
"Nie znaleziono demonstracji, jeśli wiesz gdzie jest,\n"
"uruchom ją ręcznie: python demo.py"

#: sqlkit/exc.py:48
#, fuzzy, python-format
msgid "Field '%s' cannot be NULL"
msgstr "pole %s nie może być puste (NULL)"

#: sqlkit/exc.py:60
#, fuzzy, python-format
msgid "Field '%s' cannot have value %s"
msgstr "pole %s nie może mieć wartości %s"

#: sqlkit/fields.py:979
#, python-format
msgid "value is not Decimal nor string: %s"
msgstr "wartość nie jest liczbowa ani napisem: %s"

#: sqlkit/fields.py:1055
#, python-format
msgid "value is not date nor string: %s"
msgstr "wartość nie jest datą ani napisem: %s"

#: sqlkit/fields.py:1174 sqlkit/widgets/mask/miniwidgets.py:430
#, python-format
msgid "Wrong date format: %s"
msgstr "Nieprawidłowy format daty: %s"

#: sqlkit/fields.py:1184
msgid "Wrong time format"
msgstr "Niepoprawny format czasu"

#: sqlkit/fields.py:1336
#, fuzzy
msgid "Value cannot be NULL"
msgstr "wartość '%s' nie może być użyta dla pola '%s'"

#: sqlkit/fields.py:1340
#, fuzzy, python-format
msgid "Value '%s' is not accepted"
msgstr "Wartość nie została ustawiona"

#: sqlkit/db/proxy.py:219
#, python-format
msgid "Table %s doesn't have a primary key, editing is not possible"
msgstr "Tabela %s nie ma klucza głównego, edytowanie nie jest możliwe"

#: sqlkit/layout/dateedit.py:190
msgid "Press Esc to close or double click a date to select it"
msgstr ""

#: sqlkit/layout/fk_entry.py:25
msgid "Find allowed values (Control-Enter/Shift-Enter)"
msgstr ""

#: sqlkit/layout/image_widget.py:142
#, fuzzy
msgid ""
"Right click on the image area\n"
" to upload an image"
msgstr "Użyj prawego klawisza by ponownie pokazać kolumnę "

#: sqlkit/layout/image_widget.py:273 sqlkit/widgets/table/table.py:1395
msgid "Upload image"
msgstr ""

#: sqlkit/layout/image_widget.py:273
msgid "Upload or modify an image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete the image"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Open separate image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:276
msgid "Save image as"
msgstr ""

#: sqlkit/layout/image_widget.py:279
msgid "Show/Hide image name"
msgstr ""

#: sqlkit/layout/image_widget.py:317
msgid "upload image"
msgstr ""

#: sqlkit/misc/datetools.py:152
msgid "Incomplete date format"
msgstr "Niepełny format daty"

#: sqlkit/misc/table_browser.py:135
msgid "Database"
msgstr "Baza danych"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:136 sqlkit/misc/words.py:13
#: sqlkit/widgets/common/sqlwidget.py:795
msgid "Modify"
msgstr "Modyfikuj"

#: sqlkit/misc/table_browser.py:137
msgid "Tool"
msgstr "Narzędzia"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:138 sqlkit/misc/words.py:15
#: sqlkit/widgets/common/sqlwidget.py:798
msgid "Help"
msgstr "Pomoc"

#: sqlkit/misc/table_browser.py:141
msgid "Count records"
msgstr "Policz rekordy"

#: sqlkit/misc/table_browser.py:141
msgid "Count records in all tables"
msgstr "Policz rekordy we wszystkich tabelach"

#: sqlkit/misc/table_browser.py:144
#, fuzzy
msgid ""
"Configure the fields: \n"
"labels, tooltip, completion, search field"
msgstr ""
"Konfiguruj pola: \n"
"etykiety, podpowiedź, uzupełnianie, obszar poszukiwań"

#: sqlkit/misc/table_browser.py:147
msgid "Edit Sqlkit Fields"
msgstr "Edytuj pola Sqlkit"

#: sqlkit/misc/table_browser.py:150
msgid "Primary Key"
msgstr "Klucz główny"

#: sqlkit/misc/table_browser.py:151
msgid "Show/Hide primary key if the key is numeric"
msgstr "Pokaż/Ukryj główny klucz jeśli jest numeryczny"

#: sqlkit/misc/table_browser.py:152
msgid "Load data"
msgstr "Ładuj dane"

#: sqlkit/misc/table_browser.py:153
#, fuzzy
msgid "Load the data as well"
msgstr "Wczytaj również dane"

#: sqlkit/misc/table_browser.py:154
msgid "Blank"
msgstr "Luki"

#: sqlkit/misc/table_browser.py:154
msgid "Cast blank into NULL"
msgstr "Traktuj luki jako NULL"

#: sqlkit/misc/table_browser.py:262
msgid "Mask"
msgstr "tryb Mask"

#: sqlkit/misc/table_browser.py:266
msgid "Table"
msgstr "tryb Table"

#: sqlkit/misc/table_browser.py:272
msgid "Collapse row"
msgstr ""

#: sqlkit/misc/table_browser.py:276
msgid "Table reflection"
msgstr "tryb Table reflection"

#: sqlkit/misc/table_browser.py:423
msgid "The name of the table we are customizing"
msgstr ""

#: sqlkit/misc/table_browser.py:424
#, python-format
msgid ""
"The best representation of a record \n"
"as a combination of fields, e.g.: %(title)s %(year)s"
msgstr ""

#: sqlkit/misc/table_browser.py:427
msgid "The field that will be searched for when completion is used"
msgstr ""

#. TIP: filter page of the filter panel
#: sqlkit/misc/words.py:7
msgid "filter"
msgstr "filtr"

#. TIP: output page of the filter panel
#: sqlkit/misc/words.py:9 sqlkit/widgets/common/sqlfilter.py:259
#: sqlkit/widgets/common/sqlfilter.py:306
msgid "output"
msgstr "plik wynikowy"

#. TIP: menu entry
#: sqlkit/misc/words.py:11
msgid "File"
msgstr "Plik"

#. TIP: menu entry
#: sqlkit/misc/words.py:17 sqlkit/widgets/common/sqlwidget.py:796
msgid "Go"
msgstr "Idź"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:19
msgid "opts"
msgstr "opcje"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:21 sqlkit/widgets/common/sqlwidget.py:797
msgid "Tools"
msgstr "Narzędzia"

#. TIP: serach mode:
#: sqlkit/misc/words.py:23
msgid "regexp"
msgstr "wyrażenie regularne"

#. TIP: search mode
#: sqlkit/misc/words.py:25
msgid "start"
msgstr "start"

#. TIP: count records in sqledit
#: sqlkit/misc/words.py:27
msgid "N.Rows"
msgstr "Liczba rekordów"

#: sqlkit/misc/words.py:29
msgid "Indexes"
msgstr "Indeksy"

#: sqlkit/misc/words.py:30
msgid "Nullable"
msgstr ""

#: sqlkit/misc/words.py:31
msgid "Prim. Keys"
msgstr "Klucze główne"

#: sqlkit/misc/words.py:32
msgid "Default"
msgstr "Domyślne"

#: sqlkit/misc/words.py:33
msgid "Foreign Key"
msgstr "Klucz obcy"

#: sqlkit/misc/words.py:34
msgid "filter_tables"
msgstr "filtruj _tables"

#. TIP: status bar
#: sqlkit/widgets/common/completion.py:285
#: sqlkit/widgets/common/completion.py:1124
#, python-format
msgid "search mode: %s"
msgstr "tryb szukania: %s"

#: sqlkit/widgets/common/completion.py:643
msgid "Show all info on this field, db type & Co."
msgstr "Pokaż wszystkie informacje o polu, typie bazy i podobnych"

#. TIP: menu enty in menu on right click on down arro in fkey completion widget
#: sqlkit/widgets/common/completion.py:649
msgid "Show possible values: regexp  - Ctrl-Enter"
msgstr "Pokaż możliwe wartości: wyrażenie regularne - Ctrl-Enter"

#. TIP: yellow tip to menu entry in down arrow in completion widget
#: sqlkit/widgets/common/completion.py:653
msgid "Show all values in the db that match your string"
msgstr "Pokaż wszystkie pola, które zawierają twój napis"

#: sqlkit/widgets/common/completion.py:658
msgid "Show possible values, starting - Shift-Enter"
msgstr "Pokaż możliwe wartości, rozpoczynanie - Shift-Enter"

#: sqlkit/widgets/common/completion.py:661
msgid "Show all values in the database starting with your string"
msgstr "Pokaż wszystkie wartości w bazie danych z prefiksem"

#: sqlkit/widgets/common/completion.py:667
msgid "Edit the referenced table in 'table' mode"
msgstr "Edytuj poleconą tabelę w trybie Table"

#: sqlkit/widgets/common/completion.py:669
#, fuzzy
msgid "Edit the table from which admitted values are taken in 'table' mode"
msgstr "Edytuj tabelę w trybie Table"

#: sqlkit/widgets/common/completion.py:674
msgid "Edit the referenced table in 'mask' mode"
msgstr "Edytuj poleconą tabelę w trybie Mask"

#: sqlkit/widgets/common/completion.py:676
#, fuzzy
msgid "Edit the table from which admitted values are taken in 'mask' mode"
msgstr "Edytuj tabelę w trybie Mode"

#: sqlkit/widgets/common/completion.py:783
msgid "Good match"
msgstr "Dobre dopasowanie"

#: sqlkit/widgets/common/completion.py:792
#: sqlkit/widgets/common/completion.py:798
msgid "No exact match, trying regexp completion"
msgstr "Niedokładne dopasowanie, spróbuj wyrażenia regularnego"

#: sqlkit/widgets/common/dialogs.py:26 sqlkit/widgets/common/dialogs.py:94
#: sqlkit/widgets/table/table.py:1703
msgid "All files"
msgstr "Wszystkie pliki"

#: sqlkit/widgets/common/dialogs.py:100
msgid "Images"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:106
msgid "Documents"
msgstr ""

#. TIP: possible new name for an uploaded file
#: sqlkit/widgets/common/dialogs.py:123
msgid "Preferred new filename:"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:124
msgid "If not empty, the file will be uploaded with this new name"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:107
#: sqlkit/widgets/common/sqlfilter.py:110
msgid "Match as regexp"
msgstr "wyrażenie regularne"

#: sqlkit/widgets/common/sqlfilter.py:108
msgid "Match as LIKE, \"%\" automatically added"
msgstr "LIKE, \"%\" dodane automatycznie"

#: sqlkit/widgets/common/sqlfilter.py:109
msgid "Match as LIKE case insensitive, \"%\" automatically added"
msgstr "LIKE, \"%\" dodane automatycznie (niewrażliwe na wielkość liter)"

#: sqlkit/widgets/common/sqlfilter.py:111
msgid "Match as regexp, case insensitive"
msgstr "wyrażenie regularne (niewrażliwe na wielkość liter)"

#: sqlkit/widgets/common/sqlfilter.py:112
msgid "Negation of match as regexp"
msgstr "negacja wyrażenia regularnego"

#: sqlkit/widgets/common/sqlfilter.py:113
#, fuzzy
msgid "Negation of match case insensitive"
msgstr "wrażliwe na wielkość liter"

#: sqlkit/widgets/common/sqlfilter.py:114
msgid "Equal"
msgstr "równe"

#: sqlkit/widgets/common/sqlfilter.py:115
msgid "Not equal"
msgstr "nie-równe"

#: sqlkit/widgets/common/sqlfilter.py:116
msgid "Greater then (after than)"
msgstr "większe niż"

#: sqlkit/widgets/common/sqlfilter.py:117
msgid "Greater or equal"
msgstr "większe lub równe"

#: sqlkit/widgets/common/sqlfilter.py:118
msgid "Less than (before then)"
msgstr "mniejsze niż "

#: sqlkit/widgets/common/sqlfilter.py:119
msgid "Less than or equal"
msgstr "mniejsze lub równe"

#: sqlkit/widgets/common/sqlfilter.py:120
msgid "LIKE: a \"%\" means any char - case sensitive"
msgstr "LIKE: \"%\" oznacza jakikolwiek znak- wrażliwe na wielkość liter"

#: sqlkit/widgets/common/sqlfilter.py:121
msgid "Negation of LIKE"
msgstr "zaprzeczenie LIKE"

#: sqlkit/widgets/common/sqlfilter.py:122
msgid "As LIKE but case insensitive"
msgstr "Jak LIKE lecz niewrażliwy na wielkość liter"

#: sqlkit/widgets/common/sqlfilter.py:123
msgid "Negation of ILIKE"
msgstr "negacja ILIKE"

#: sqlkit/widgets/common/sqlfilter.py:124
msgid "The boolean is True"
msgstr "Wartość logiczna: Prawda"

#: sqlkit/widgets/common/sqlfilter.py:125
msgid "The boolean is False"
msgstr "Wartość logiczna: Fałsz"

#: sqlkit/widgets/common/sqlfilter.py:126
msgid "The boolean is not True"
msgstr "Wartość logiczna nie jest Prawdą"

#: sqlkit/widgets/common/sqlfilter.py:127
msgid "The boolean is not False"
msgstr "Wartość logiczna nie jest Fałszem"

#: sqlkit/widgets/common/sqlfilter.py:128
msgid "The value is not set"
msgstr "Wartość nie została ustawiona"

#: sqlkit/widgets/common/sqlfilter.py:129
msgid "The value is set"
msgstr "Wartość została ustawiona"

#: sqlkit/widgets/common/sqlfilter.py:130
msgid "ID equality (don't follow foreign table)"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:221
msgid "Filter Panel"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlfilter.py:230
msgid "Add filters for your query"
msgstr "Dodaj filtr do zapytania"

#: sqlkit/widgets/common/sqlfilter.py:232
msgid "Result page for your query"
msgstr "Strona wynikowa dla twojego zapytania"

#: sqlkit/widgets/common/sqlfilter.py:269
#, fuzzy
msgid "Filter actions"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlfilter.py:270
msgid "Reload from db"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:271
#, fuzzy
msgid "Close the panel"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlfilter.py:274
#, fuzzy
msgid "Go to filter panel"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlfilter.py:276
msgid "Go to output panel"
msgstr ""

#. TIP: status bar message
#: sqlkit/widgets/common/sqlfilter.py:479 sqlkit/widgets/table/table.py:762
#: sqlkit/widgets/table/table.py:1039
#, python-format
msgid "Total N. of records: %s"
msgstr "Łączna liczba rekordów: %s"

#: sqlkit/widgets/common/sqlfilter.py:801
#, python-format
msgid "value '%s' cannot be used for field '%s'"
msgstr "wartość '%s' nie może być użyta dla pola '%s'"

#: sqlkit/widgets/common/sqlfilter.py:868
#, python-format
msgid ""
"value '%s' does not seem a valid date and cannot be transformed into a "
"date"
msgstr ""
"wartość '%s' nie wygląda na poprawną datę i nie może być w nią "
"przekształcona"

#. TIP: appears in the menu in the filter panel to add a second entry of the
#. same field
#: sqlkit/widgets/common/sqlfilter.py:1042
#, python-format
msgid "Add a new filter on this field '%s'"
msgstr "Dodaj nowy filtr na pole '%s'"

#: sqlkit/widgets/common/sqlfilter.py:1048
msgid "Use this filter"
msgstr "Użyj tego filtra"

#: sqlkit/widgets/common/sqlfilter.py:1092
msgid "Click here to select an operator"
msgstr "Kliknij by wybrać operatora"

#: sqlkit/widgets/common/sqlfilter.py:1164
msgid "incomplete date format"
msgstr "niekompletny format daty"

#: sqlkit/widgets/common/sqlwidget.py:358
#, fuzzy, python-format
msgid ""
"Sorry, problems connecting to remote db. Original error was: \n"
"\n"
"%s"
msgstr ""
"Błąd podczas zapisu do bazy danych: \n"
"czy 'zapisujesz jako nowy' istniejący już rekord?\n"
"błąd w oryginale: \n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:625
msgid "Hiding field is only supported for Tables"
msgstr "Ukrywanie pól dostępne jedynie dla trybu Tables"

#: sqlkit/widgets/common/sqlwidget.py:792
msgid "Show all the differences that should be saved to database"
msgstr "Pokaż wszystkie różnice, które powinny być zapisane do bazy"

#: sqlkit/widgets/common/sqlwidget.py:802
msgid "Pending Differences"
msgstr "Zmiany w toku"

#: sqlkit/widgets/common/sqlwidget.py:806
#, fuzzy
msgid "Save current record"
msgstr "Policz rekordy"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Filter panel"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlwidget.py:810
#, fuzzy
msgid "Add filter panel"
msgstr "Panel filtrowania"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload"
msgstr "Przeładuj"

#: sqlkit/widgets/common/sqlwidget.py:811
#, fuzzy
msgid "Reload from the database"
msgstr "Wczytaj również dane"

#: sqlkit/widgets/common/sqlwidget.py:815
#, fuzzy
msgid "Go to next record"
msgstr "Policz rekordy"

#: sqlkit/widgets/common/sqlwidget.py:816
msgid "Go to previous record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:823
msgid "Inspect widgets"
msgstr "Inspektor obiektów"

#: sqlkit/widgets/common/sqlwidget.py:990
msgid "Saved"
msgstr "Zachowano"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1020
#, fuzzy, python-format
msgid ""
"Error while writing to the database. \n"
"Original error was: \n"
"%s"
msgstr ""
"Błąd podczas zapisu do bazy danych: \n"
"czy 'zapisujesz jako nowy' istniejący już rekord?\n"
"błąd w oryginale: \n"
"%s"

#. TIP: Error while saving into a table w/o permission
#. TIP: reloading data from the database
#: sqlkit/widgets/common/sqlwidget.py:1026
#: sqlkit/widgets/common/sqlwidget.py:1563
#, python-format
msgid ""
"A programming error was received from the backend:\n"
"%s"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1035
#, python-format
msgid "%s - Dirty objects: %s - New objects: %s"
msgstr "%s - Zużywane obiekty: %s - Nowe obiekty: %s"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1041
#, python-format
msgid ""
"Error while writing to the database: \n"
"are you 'saving as new' an already known record?\n"
"original error was: \n"
"%s"
msgstr ""
"Błąd podczas zapisu do bazy danych: \n"
"czy 'zapisujesz jako nowy' istniejący już rekord?\n"
"błąd w oryginale: \n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:1186
msgid "Discarding new obj"
msgstr "Zawetuj nowy objekt"

#: sqlkit/widgets/common/sqlwidget.py:1223
#, python-format
msgid "Char %s is not accepted in numeric context"
msgstr "Znak %s nie jest akceptowalny w kontekście numerycznym"

#: sqlkit/widgets/common/sqlwidget.py:1281
msgid "record validation"
msgstr "potwierdzanie rekordu"

#: sqlkit/widgets/common/sqlwidget.py:1503
msgid "Unsaved differences"
msgstr "Niezachowane różnice"

#: sqlkit/widgets/common/sqlwidget.py:1741
msgid "Sorry, export is implemented only for table view"
msgstr "Niestety, eksport jest zaimplementowany jedynie dla trybu Table"

#: sqlkit/widgets/common/sqlwidget.py:2097
msgid "Property"
msgstr "Własności"

#: sqlkit/widgets/common/sqlwidget.py:2098
msgid "Value"
msgstr "Wartość"

#: sqlkit/widgets/common/sqlwidget.py:2184
msgid "Save unsaved data?"
msgstr "Zapisać niezachowane dane?"

#: sqlkit/widgets/common/sqlwidget.py:2187
msgid "Unsaved data"
msgstr "Niezachowane dane"

#: sqlkit/widgets/common/sqlwidget.py:2209
msgid "State"
msgstr "Stan"

#: sqlkit/widgets/common/sqlwidget.py:2210
#: sqlkit/widgets/common/sqlwidget.py:2330
msgid "Field name"
msgstr "Nazwa pola"

#: sqlkit/widgets/common/sqlwidget.py:2211
msgid "Original value"
msgstr "Oryginalna wartość"

#: sqlkit/widgets/common/sqlwidget.py:2212
msgid "Present value"
msgstr "Obecna wartość"

#: sqlkit/widgets/common/sqlwidget.py:2242
msgid "Modified"
msgstr "Zmodyfikowane"

#: sqlkit/widgets/common/sqlwidget.py:2269 sqlkit/widgets/mask/mask.py:597
msgid "Deleted"
msgstr "Usunięte"

#: sqlkit/widgets/common/sqlwidget.py:2279
msgid "New"
msgstr "Nowe"

#: sqlkit/widgets/common/sqlwidget.py:2293
msgid "empty string: ''"
msgstr "pusty napis: ''"

#: sqlkit/widgets/common/sqlwidget.py:2295
msgid "NULL value"
msgstr "wartość pusta"

#: sqlkit/widgets/common/sqlwidget.py:2301
msgid ""
"Errors are present in the record. \n"
"Correct them now, to continue \n"
"or delete the record"
msgstr ""
"W rekordzie występują błędy. \n"
"Popraw je, by kontynuować \n"
"lub usuń rekord"

#: sqlkit/widgets/common/sqlwidget.py:2302
msgid "Validation errors"
msgstr "Błędy poprawności składniowej"

#: sqlkit/widgets/common/sqlwidget.py:2331
msgid "Error"
msgstr "Błąd"

#: sqlkit/widgets/common/sqlwidget.py:2362
msgid ""
"You can continue or go back editing. \n"
"Read the following warnings to decide"
msgstr ""
"Możesz kontynuować lub wrócić do edycji. \n"
"Przeczytaj następujące ostrzeżenia by zdecydować"

#: sqlkit/widgets/common/sqlwidget.py:2363
msgid "Validation Warnings"
msgstr "Ostrzeżenia kontrolera poprawności"

#: sqlkit/widgets/mask/mask.py:258
#, fuzzy
msgid "Add new record"
msgstr "Aktualnie w nowym rekordzie"

#: sqlkit/widgets/mask/mask.py:262
msgid "Discard changes"
msgstr ""

#. TIP Modify menu entry in the mask to reread a single record from database
#: sqlkit/widgets/mask/mask.py:264
#, fuzzy
msgid "Refresh this record"
msgstr ""
"Usunąć ten rekord?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:265
#, fuzzy
msgid "Reread this record from db"
msgstr "Aktualnie pierwszy rekord"

#: sqlkit/widgets/mask/mask.py:268
#, fuzzy
msgid "Delete this record"
msgstr ""
"Usunąć ten rekord?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:342 sqlkit/widgets/table/table.py:908
msgid "Already at new record"
msgstr "Aktualnie w nowym rekordzie"

#: sqlkit/widgets/mask/mask.py:362
#, python-format
msgid "New record %s"
msgstr "Nowy rekord %s"

#. TIP message issued when a refresh is done on a deleted record
#: sqlkit/widgets/mask/mask.py:405
msgid "The record is no longer present in the database"
msgstr ""

#: sqlkit/widgets/mask/mask.py:473 sqlkit/widgets/table/table.py:838
msgid "Nothing to save"
msgstr "Nie ma nic do zachowania"

#: sqlkit/widgets/mask/mask.py:523
#, python-format
msgid "Primary key (%s) didn't change. Refusing to save as new"
msgstr "Klucz główny (%s) nie zmienił się. Odmowa dla zapisu nowego"

#: sqlkit/widgets/mask/mask.py:537
msgid "Do you want to copy all data to a new record?"
msgstr ""

#: sqlkit/widgets/mask/mask.py:585 sqlkit/widgets/table/table.py:982
#, python-format
msgid ""
"Delete this record?\n"
"(%s)"
msgstr ""
"Usunąć ten rekord?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:654
msgid "No record present"
msgstr "Brak rekordów do pokazania"

#: sqlkit/widgets/mask/mask.py:677
msgid "Already last record"
msgstr "Aktualnie ostatni rekord"

#: sqlkit/widgets/mask/mask.py:679
msgid "Already first record"
msgstr "Aktualnie pierwszy rekord"

#: sqlkit/widgets/mask/miniwidgets.py:695
#, python-format
msgid "A file with name '%s' already exists. Overwrite?"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:696
msgid "Upload name conflict"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:888
#, python-format
msgid "'%s' may have an invalid value: try completion on that"
msgstr "'%s' może mieć niepoprawną wartość: spróbuj uzupełnić"

#: sqlkit/widgets/table/columns.py:208
msgid "Editing canceled. Restoring original value"
msgstr "Edytowanie wstrzymane. Przywracanie oryginalnej wartości"

#: sqlkit/widgets/table/columns.py:848
#, python-format
msgid "Add a filter on '%s'"
msgstr "Dodaj filtr na '%s'"

#: sqlkit/widgets/table/columns.py:858
msgid ""
"Sort on this column reloading from the database, you can used 's' to sort"
" locally"
msgstr ""

#: sqlkit/widgets/table/columns.py:861
msgid "Sort on this column locally (w/o touching the database)"
msgstr ""

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:871
msgid "Hide this column"
msgstr "Ukryj tę kolumnę"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:878
msgid "Create total"
msgstr "Stworzono łącznie"

#. TIP: column menu total
#: sqlkit/widgets/table/columns.py:888
#, python-format
msgid "Subtotal on %s"
msgstr "Częściowo %s"

#. TIP: column menu opt
#. TIP: modify menu entry
#: sqlkit/widgets/table/columns.py:910 sqlkit/widgets/table/columns.py:1293
#: sqlkit/widgets/table/table.py:287
msgid "Show field"
msgstr "Pokaż pole"

#: sqlkit/widgets/table/columns.py:911 sqlkit/widgets/table/columns.py:1294
#: sqlkit/widgets/table/table.py:288
msgid "Hide field"
msgstr "Ukryj pole"

#: sqlkit/widgets/table/columns.py:938
msgid "day"
msgstr "dzień"

#: sqlkit/widgets/table/columns.py:938
msgid "week"
msgstr "tydzień"

#: sqlkit/widgets/table/columns.py:938
msgid "month"
msgstr "miesiąc"

#: sqlkit/widgets/table/columns.py:939
msgid "quarter"
msgstr "kwartał"

#: sqlkit/widgets/table/columns.py:939
msgid "year"
msgstr "rok"

#: sqlkit/widgets/table/columns.py:942
#, python-format
msgid "Subtotals by %s"
msgstr "Częściowo przez %s"

#: sqlkit/widgets/table/columns.py:1267
msgid "Right click on this table to show the column again"
msgstr "Użyj prawego klawisza by ponownie pokazać kolumnę "

#: sqlkit/widgets/table/columns.py:1287 sqlkit/widgets/table/table.py:301
msgid "Export"
msgstr "Eksport"

#: sqlkit/widgets/table/columns.py:1287
msgid "Export these data into csv format"
msgstr ""

#: sqlkit/widgets/table/columns.py:1292
msgid "Adapt width of columns to data"
msgstr ""

#: sqlkit/widgets/table/columns.py:1321
#, python-format
msgid "View '%s' in a Mask"
msgstr "Zobacz '%s' w trybie Mask"

#: sqlkit/widgets/table/table.py:274
msgid "Duplicate"
msgstr ""

#: sqlkit/widgets/table/table.py:275
msgid "Create a new row as a duplicate of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:277
msgid "New child row"
msgstr ""

#: sqlkit/widgets/table/table.py:278
msgid "Create e new row as child of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:292
msgid "View this record in a Mask"
msgstr "Zobacz rekord w trybie Mask"

#: sqlkit/widgets/table/table.py:293
msgid "View this ForeignKey in a Mask"
msgstr "Zobacz klucz obcy w trybie Mask"

#: sqlkit/widgets/table/table.py:294
msgid "Upload Image"
msgstr ""

#: sqlkit/widgets/table/table.py:295
#, fuzzy
msgid "Upload File"
msgstr "Wszystkie pliki"

#: sqlkit/widgets/table/table.py:296
msgid "Delete File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:297
msgid "Show File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save file locally"
msgstr ""

#: sqlkit/widgets/table/table.py:528 sqlkit/widgets/table/table.py:542
#: sqlkit/widgets/table/table.py:554
msgid "Insertion of new records is disabled. Sorry"
msgstr "Niestety, dodawanie nowych rekordów jest wyłączone."

#: sqlkit/widgets/table/table.py:558
msgid "Update of records is disabled. Sorry"
msgstr "Niestety, aktualizacja rekordów jest wyłączona."

#: sqlkit/widgets/table/table.py:570
msgid "Deletion of records is disabled. Sorry"
msgstr "Niestety, usuwanie rekordów jest wyłączone."

#: sqlkit/widgets/table/table.py:583
msgid "browsing of new records is disabled. Sorry"
msgstr "Niestety, przeglądanie rekordów jest wyłączone."

#: sqlkit/widgets/table/table.py:731
msgid "no current obj: maybe no record has yet been edited"
msgstr "nie ma obiektu do zaznaczenia: może żaden nie był jeszcze opisany"

#. TIP: when saving m2m, we delay till leader record will be saved
#: sqlkit/widgets/table/table.py:869
msgid "delaying saving when main record will be saved"
msgstr "opóźnianie zapisu jeśli główny rekord został zachowany"

#: sqlkit/widgets/table/table.py:879
msgid "Record has NOT been saved"
msgstr "Rekord NIE został zachowany"

#: sqlkit/widgets/table/table.py:978
msgid "No record selected"
msgstr "Nie zaznaczono rekordu"

#: sqlkit/widgets/table/table.py:1137
msgid "Value is not valid, trying completion"
msgstr "Wartość nie jest poprawna, próba uzupełnienia"

#. TIP: check in input field length %s %s -> field_name length
#: sqlkit/widgets/table/table.py:1170
#, python-format
msgid "Input exceeded max %s length (%s)"
msgstr "Maksymalna długość %s została przekroczona (%s)"

#: sqlkit/widgets/table/table.py:1284
#, fuzzy
msgid "Unsaved data prevent opening a Mask to show the (unsaved) record"
msgstr "Niezachowane dane zapobiegają otworzeniu trybu Mask "

#: sqlkit/widgets/table/table.py:1407
#, python-format
msgid ""
"A file with name '%s' already exists.\n"
"Overwrite?"
msgstr ""

#: sqlkit/widgets/table/table.py:1408
msgid "Upload name duplication"
msgstr ""

#: sqlkit/widgets/table/table.py:1629
msgid "Export in csv file"
msgstr "Eksportuj do pliku csv"

#: sqlkit/widgets/table/table.py:1702
msgid "Csv files"
msgstr "Pliki csv"

#: sqlkit/widgets/table/tablewidgets.py:34
#, python-format
msgid "No current obj for field '%s' in %s"
msgstr "Nie ma obiektu dla pola '%s' in %s"

