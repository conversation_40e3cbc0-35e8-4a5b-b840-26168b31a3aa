# German translations for PROJECT.
# Copyright (C) 2011 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2011.
# <AUTHOR> <EMAIL>, 2011.
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2011-03-03 12:33+0100\n"
"PO-Revision-Date: 2011-03-13 00:10+0200\n"
"Last-Translator: Sandro <<EMAIL>>\n"
"Language-Team: sqlkit\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Virtaal 0.5.2\n"
"Generated-By: Babel 0.9.4\n"

#: bin/sqledit.py:87
msgid "Connection setup"
msgstr "Verbindungsaufbau"

#: bin/sqledit.py:90
msgid ""
"\n"
"    You can indicate the database you want to connect to\n"
"    using an URI in the form as:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    or using password and username:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "
msgstr ""
"\n"
"    Sie können die Datenbank für die Verbindung angeben\n"
"    mit einer URI in der Form von:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    oder mit Passwort und Benutzername:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "

#: bin/sqledit.py:117
msgid "Run Demo"
msgstr "Demo ausführen"

#: bin/sqledit.py:140
msgid ""
"Info on available backend:\n"
"postgres, mysql..."
msgstr ""
"Information zu verfügbaren \"Backends\":\n"
"postgres, mysql..."

#: bin/sqledit.py:141
msgid "Sqledit manual page"
msgstr "sqledit Handbuch"

#: bin/sqledit.py:146
msgid ""
"Try continuosly to connect.\n"
"        Nice and usefull but may cause temporary blocks\n"
"        if you write an inexistent hostname\n"
"        "
msgstr ""
"Versuch ständig eine Verbindung herzustellen.\n"
"        Nützlich, kann aber eine vorübergehende Blockierung verursachen\n"
"        wenn Sie einen nicht existierenden host Namen eingeben.\n"

#: bin/sqledit.py:160
msgid ""
"If you write a wrong hostname\n"
"the application may hang some seconds till the natural network timeout. \n"
"Uncheck the flag on the right ti disable this feature"
msgstr ""
"Wenn Sie einen falschen host Namen eingeben\n"
"kann es sein das die Applikation für einige Sekunden blockiert (hängt vom"
" Network timeout ab).\n"
"Diese Funktion können Sie rechts deaktivieren."

#: bin/sqledit.py:187
#, python-format
msgid "Attempting to connect to %s"
msgstr "Versuche Verbindung mit %s aufzubauen"

#: bin/sqledit.py:190
#, python-format
msgid "Connected to %s"
msgstr "'Verbunden zu %s"

#: bin/sqledit.py:221
msgid "A complete demo of all the features of the sqlkit package"
msgstr "Eine komplette Demostration aller Funktionen des sqlkit Packages."

#: bin/sqledit.py:222
msgid ""
"The demo was not found, if you know where it is,\n"
"run it manually: python demo.py"
msgstr ""
"'Die Demonstration wurde nicht gefunden, falls Sie wissen wo Sie ist\n"
"können Sie manuel ausführen: python demo.py"

#: sqlkit/exc.py:48
#, python-format
msgid "Field '%s' cannot be NULL"
msgstr "Feld '%s' kann nicht NULL sein"

#: sqlkit/exc.py:60
#, python-format
msgid "Field '%s' cannot have value %s"
msgstr "Feld '%s' kann nicht diesen Wert '%s' haben"

#: sqlkit/fields.py:979
#, python-format
msgid "value is not Decimal nor string: %s"
msgstr "Wert ist nicht Decimal oder eine Zeichenfolge: %s"

#: sqlkit/fields.py:1055
#, python-format
msgid "value is not date nor string: %s"
msgstr "Wert ist nicht ein Datum oder eine Zeichenfolge: %s"

#: sqlkit/fields.py:1174 sqlkit/widgets/mask/miniwidgets.py:430
#, python-format
msgid "Wrong date format: %s"
msgstr "Falsches Datumsformat: %s"

#: sqlkit/fields.py:1184
msgid "Wrong time format"
msgstr "Falsches Zeitformat"

#: sqlkit/fields.py:1336
msgid "Value cannot be NULL"
msgstr "Wert kann nicht NULL sein"

#: sqlkit/fields.py:1340
#, python-format
msgid "Value '%s' is not accepted"
msgstr "Wert '%s' ist nicht akzeptiert"

#: sqlkit/db/proxy.py:219
#, python-format
msgid "Table %s doesn't have a primary key, editing is not possible"
msgstr "Tabelle '%s' hat keinen Primärschlüssel, Bearbeiten ist nicht möglich"

#: sqlkit/layout/dateedit.py:190
msgid "Press Esc to close or double click a date to select it"
msgstr ""
"Klicken Sie Esc um zu schliessen oder Doppelklick ein Datum um es zu "
"selektieren"

#: sqlkit/layout/fk_entry.py:25
msgid "Find allowed values (Control-Enter/Shift-Enter)"
msgstr "Finde erlaubte Werte  (Control-Enter/Shift-Enter)"

#: sqlkit/layout/image_widget.py:142
msgid ""
"Right click on the image area\n"
" to upload an image"
msgstr ""
"Rechts Klicken auf dem Bild\n"
"um das Bild hoch zu laden"

#: sqlkit/layout/image_widget.py:273 sqlkit/widgets/table/table.py:1395
msgid "Upload image"
msgstr "Bild hochladen"

#: sqlkit/layout/image_widget.py:273
msgid "Upload or modify an image"
msgstr "Bild hochladen oder ändern"

#: sqlkit/layout/image_widget.py:274
msgid "Delete image"
msgstr "Bild löschen"

#: sqlkit/layout/image_widget.py:274
msgid "Delete the image"
msgstr "Das Bild löschen"

#: sqlkit/layout/image_widget.py:275
msgid "Image viewer"
msgstr "Bildbetrachter"

#: sqlkit/layout/image_widget.py:275
msgid "Open separate image viewer"
msgstr "Einen separaten Bildbetrachter öffnen"

#: sqlkit/layout/image_widget.py:276
msgid "Save image as"
msgstr "Bild speichern unter"

#: sqlkit/layout/image_widget.py:279
msgid "Show/Hide image name"
msgstr "Anzeigen/Verbergen Bildname"

#: sqlkit/layout/image_widget.py:317
msgid "upload image"
msgstr "Bild hochladen"

#: sqlkit/misc/datetools.py:152
msgid "Incomplete date format"
msgstr "Unvollständiges Datumsformat"

#: sqlkit/misc/table_browser.py:135
msgid "Database"
msgstr "Datenbank"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:136 sqlkit/misc/words.py:13
#: sqlkit/widgets/common/sqlwidget.py:795
msgid "Modify"
msgstr "Verändern"

#: sqlkit/misc/table_browser.py:137
msgid "Tool"
msgstr "Werkzeug"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:138 sqlkit/misc/words.py:15
#: sqlkit/widgets/common/sqlwidget.py:798
msgid "Help"
msgstr "Hilfe"

#: sqlkit/misc/table_browser.py:141
msgid "Count records"
msgstr "Anzahl Datensätze"

#: sqlkit/misc/table_browser.py:141
msgid "Count records in all tables"
msgstr "Anzahl Datensätze in allen Tabellen"

#: sqlkit/misc/table_browser.py:144
msgid ""
"Configure the fields: \n"
"labels, tooltip, completion, search field"
msgstr ""
"Die Felder konfigurieren:\n"
"Kennzeichen, Tooltip, Vervollständigung, Suchfeld"

#: sqlkit/misc/table_browser.py:147
msgid "Edit Sqlkit Fields"
msgstr "Bearbeiten sqlkit Felder"

#: sqlkit/misc/table_browser.py:150
msgid "Primary Key"
msgstr "Primärschlüssel"

#: sqlkit/misc/table_browser.py:151
msgid "Show/Hide primary key if the key is numeric"
msgstr "Anzeigen/Verbergen Primärschlüssel wenn der Schlüssel numerisch ist"

#: sqlkit/misc/table_browser.py:152
msgid "Load data"
msgstr "Laden der Daten"

#: sqlkit/misc/table_browser.py:153
msgid "Load the data as well"
msgstr "Laden auch der Daten"

#: sqlkit/misc/table_browser.py:154
msgid "Blank"
msgstr "Leer"

#: sqlkit/misc/table_browser.py:154
msgid "Cast blank into NULL"
msgstr "Cast \"leer\" zu NULL"

#: sqlkit/misc/table_browser.py:262
msgid "Mask"
msgstr "Maske"

#: sqlkit/misc/table_browser.py:266
msgid "Table"
msgstr "Tabelle"

#: sqlkit/misc/table_browser.py:272
msgid "Collapse row"
msgstr "Zeile zusammenklappen"

#: sqlkit/misc/table_browser.py:276
msgid "Table reflection"
msgstr "Tabelle Reflexion"

#: sqlkit/misc/table_browser.py:423
msgid "The name of the table we are customizing"
msgstr "Der Name der Tabelle die wir anpassen"

#: sqlkit/misc/table_browser.py:424
#, python-format
msgid ""
"The best representation of a record \n"
"as a combination of fields, e.g.: %(title)s %(year)s"
msgstr ""
"Die beste Darstellung eines Datensatzes\n"
"als eine Kombination von Feldern, z.B. %(title)s %(year)s"

#: sqlkit/misc/table_browser.py:427
msgid "The field that will be searched for when completion is used"
msgstr "Das Feld das gesucht wird wenn Vervollständigung benutzt wird"

#. TIP: filter page of the filter panel
#: sqlkit/misc/words.py:7
msgid "filter"
msgstr "Filter"

#. TIP: output page of the filter panel
#: sqlkit/misc/words.py:9 sqlkit/widgets/common/sqlfilter.py:259
#: sqlkit/widgets/common/sqlfilter.py:306
msgid "output"
msgstr "Ausgabe"

#. TIP: menu entry
#: sqlkit/misc/words.py:11
msgid "File"
msgstr "Datei"

#. TIP: menu entry
#: sqlkit/misc/words.py:17 sqlkit/widgets/common/sqlwidget.py:796
msgid "Go"
msgstr "Go"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:19
msgid "opts"
msgstr "opts"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:21 sqlkit/widgets/common/sqlwidget.py:797
msgid "Tools"
msgstr "Werkzeuge"

#. TIP: serach mode:
#: sqlkit/misc/words.py:23
msgid "regexp"
msgstr "'regexp'"

#. TIP: search mode
#: sqlkit/misc/words.py:25
msgid "start"
msgstr "Beginn"

#. TIP: count records in sqledit
#: sqlkit/misc/words.py:27
msgid "N.Rows"
msgstr "N. Zeilen"

#: sqlkit/misc/words.py:29
msgid "Indexes"
msgstr "Indizes"

#: sqlkit/misc/words.py:30
msgid "Nullable"
msgstr "Nullable"

#: sqlkit/misc/words.py:31
msgid "Prim. Keys"
msgstr "Primärschlüssel"

#: sqlkit/misc/words.py:32
msgid "Default"
msgstr "Vorgabe"

#: sqlkit/misc/words.py:33
msgid "Foreign Key"
msgstr "Fremdschlüssel"

#: sqlkit/misc/words.py:34
msgid "filter_tables"
msgstr "Filter_Tabellen"

#. TIP: status bar
#: sqlkit/widgets/common/completion.py:285
#: sqlkit/widgets/common/completion.py:1124
#, python-format
msgid "search mode: %s"
msgstr "Such modus: %s"

#: sqlkit/widgets/common/completion.py:643
msgid "Show all info on this field, db type & Co."
msgstr "Alle Informationen zu diesem Feld anzeigen, db, type & Co."

#. TIP: menu enty in menu on right click on down arro in fkey completion widget
#: sqlkit/widgets/common/completion.py:649
msgid "Show possible values: regexp  - Ctrl-Enter"
msgstr "Zeige mögliche Werte an: 'regexp' - Ctrl-Enter"

#. TIP: yellow tip to menu entry in down arrow in completion widget
#: sqlkit/widgets/common/completion.py:653
msgid "Show all values in the db that match your string"
msgstr "Zeige alle Werte in der Datenbank an, die der Zeichenfolge entsprechen"

#: sqlkit/widgets/common/completion.py:658
msgid "Show possible values, starting - Shift-Enter"
msgstr "Zeige mögliche Werte an, anfangen - Shift-Enter"

#: sqlkit/widgets/common/completion.py:661
msgid "Show all values in the database starting with your string"
msgstr "Zeige alle Werte in der Datenbank die mit Ihrer Zeichenfolge anfangen"

#: sqlkit/widgets/common/completion.py:667
msgid "Edit the referenced table in 'table' mode"
msgstr "Ändere die referenz Tabelle im \"Tabellen\" Modus"

#: sqlkit/widgets/common/completion.py:669
msgid "Edit the table from which admitted values are taken in 'table' mode"
msgstr ""
"Ändere die Tabelle von welcher gültige Werte entnommen werden im "
"'Tabellen' Modus"

#: sqlkit/widgets/common/completion.py:674
msgid "Edit the referenced table in 'mask' mode"
msgstr "Änder die referenz Tabelle im 'Maske\" Modus"

#: sqlkit/widgets/common/completion.py:676
msgid "Edit the table from which admitted values are taken in 'mask' mode"
msgstr ""
"Ändere die Tabelle von welcher gültige Werte entnommen werden im 'Masken'"
" Modus"

#: sqlkit/widgets/common/completion.py:783
msgid "Good match"
msgstr "Guter 'match'"

#: sqlkit/widgets/common/completion.py:792
#: sqlkit/widgets/common/completion.py:798
msgid "No exact match, trying regexp completion"
msgstr "Kein genauer 'match', versuche 'regexp' Vervollständigung"

#: sqlkit/widgets/common/dialogs.py:26 sqlkit/widgets/common/dialogs.py:94
#: sqlkit/widgets/table/table.py:1703
msgid "All files"
msgstr "Alle Dateien"

#: sqlkit/widgets/common/dialogs.py:100
msgid "Images"
msgstr "Bilder"

#: sqlkit/widgets/common/dialogs.py:106
msgid "Documents"
msgstr "Dokumente"

#. TIP: possible new name for an uploaded file
#: sqlkit/widgets/common/dialogs.py:123
msgid "Preferred new filename:"
msgstr "Bevorzugter neuer Dateiname:"

#: sqlkit/widgets/common/dialogs.py:124
msgid "If not empty, the file will be uploaded with this new name"
msgstr "Falls nicht leer wird die Datei mit diesem neuen Namen hochgeladen"

#: sqlkit/widgets/common/sqlfilter.py:107
#: sqlkit/widgets/common/sqlfilter.py:110
msgid "Match as regexp"
msgstr "'match' als 'regexp'"

#: sqlkit/widgets/common/sqlfilter.py:108
msgid "Match as LIKE, \"%\" automatically added"
msgstr "'match' als 'LIKE', \"%\" wird automatisch hinzugefügt"

#: sqlkit/widgets/common/sqlfilter.py:109
msgid "Match as LIKE case insensitive, \"%\" automatically added"
msgstr "'match' als 'LIKE' case insensitive, \"%\" wird automatisch hinzugefügt"

#: sqlkit/widgets/common/sqlfilter.py:111
msgid "Match as regexp, case insensitive"
msgstr "'match' als 'regexp' - case insensitive"

#: sqlkit/widgets/common/sqlfilter.py:112
msgid "Negation of match as regexp"
msgstr "Die Negation von 'match' als 'regexp'"

#: sqlkit/widgets/common/sqlfilter.py:113
msgid "Negation of match case insensitive"
msgstr "Die Negation von 'match' - case insensitive"

#: sqlkit/widgets/common/sqlfilter.py:114
msgid "Equal"
msgstr "Gleichwertig"

#: sqlkit/widgets/common/sqlfilter.py:115
msgid "Not equal"
msgstr "Nicht gleichwertig"

#: sqlkit/widgets/common/sqlfilter.py:116
msgid "Greater then (after than)"
msgstr "Grösser als (nach als)"

#: sqlkit/widgets/common/sqlfilter.py:117
msgid "Greater or equal"
msgstr "Grösser oder gleichwertig"

#: sqlkit/widgets/common/sqlfilter.py:118
msgid "Less than (before then)"
msgstr "Kleiner als (bevor dann)"

#: sqlkit/widgets/common/sqlfilter.py:119
msgid "Less than or equal"
msgstr "Kleiner als oder gleichwertig"

#: sqlkit/widgets/common/sqlfilter.py:120
msgid "LIKE: a \"%\" means any char - case sensitive"
msgstr "LIKE: das \"%\" bedeutet ein beliebiges Zeichen - case sensitive "

#: sqlkit/widgets/common/sqlfilter.py:121
msgid "Negation of LIKE"
msgstr "Die Negation von LIKE"

#: sqlkit/widgets/common/sqlfilter.py:122
msgid "As LIKE but case insensitive"
msgstr "Wie LIKE aber case insensitive"

#: sqlkit/widgets/common/sqlfilter.py:123
msgid "Negation of ILIKE"
msgstr "Die Negation von ILIKE"

#: sqlkit/widgets/common/sqlfilter.py:124
msgid "The boolean is True"
msgstr "Der Boolean ist 'True'"

#: sqlkit/widgets/common/sqlfilter.py:125
msgid "The boolean is False"
msgstr "Der Boolean ist 'False'"

#: sqlkit/widgets/common/sqlfilter.py:126
msgid "The boolean is not True"
msgstr "Der Boolean ist nicht 'True'"

#: sqlkit/widgets/common/sqlfilter.py:127
msgid "The boolean is not False"
msgstr "Der Boolean ist nicht 'False'"

#: sqlkit/widgets/common/sqlfilter.py:128
msgid "The value is not set"
msgstr "Der Wert wurde nicht gesetzt"

#: sqlkit/widgets/common/sqlfilter.py:129
msgid "The value is set"
msgstr "Der Wert wurde gesetzt"

#: sqlkit/widgets/common/sqlfilter.py:130
msgid "ID equality (don't follow foreign table)"
msgstr "ID Gleichheit (der Fremdtabelle nicht nachgehen)"

#: sqlkit/widgets/common/sqlfilter.py:221
msgid "Filter Panel"
msgstr "Filter Panel"

#: sqlkit/widgets/common/sqlfilter.py:230
msgid "Add filters for your query"
msgstr "Add Filter für Ihre Anfrage"

#: sqlkit/widgets/common/sqlfilter.py:232
msgid "Result page for your query"
msgstr "Resultat Seite für Ihre Anfrage"

#: sqlkit/widgets/common/sqlfilter.py:269
msgid "Filter actions"
msgstr "Filter Aktionen"

#: sqlkit/widgets/common/sqlfilter.py:270
msgid "Reload from db"
msgstr "Neu Laden von der Datenbank"

#: sqlkit/widgets/common/sqlfilter.py:271
msgid "Close the panel"
msgstr "Das Panel schliessen"

#: sqlkit/widgets/common/sqlfilter.py:274
msgid "Go to filter panel"
msgstr "Zum Filter Panel gehen"

#: sqlkit/widgets/common/sqlfilter.py:276
msgid "Go to output panel"
msgstr "Zum Ausgabe Panel gehen"

#. TIP: status bar message
#: sqlkit/widgets/common/sqlfilter.py:479 sqlkit/widgets/table/table.py:762
#: sqlkit/widgets/table/table.py:1039
#, python-format
msgid "Total N. of records: %s"
msgstr "Anzahl von Datensätzen: %s"

#: sqlkit/widgets/common/sqlfilter.py:801
#, python-format
msgid "value '%s' cannot be used for field '%s'"
msgstr "Wert '%s' kann für diese Feld '%s' nicht benutz werden"

#: sqlkit/widgets/common/sqlfilter.py:868
#, python-format
msgid ""
"value '%s' does not seem a valid date and cannot be transformed into a "
"date"
msgstr ""
"Wert '%s' schein kein gültiges Datum zu sein und kann nicht in ein Datum "
"transformiert werden"

#. TIP: appears in the menu in the filter panel to add a second entry of the
#. same field
#: sqlkit/widgets/common/sqlfilter.py:1042
#, python-format
msgid "Add a new filter on this field '%s'"
msgstr "Einen neun Filter für dieses Feld '%s' hinzufügen"

#: sqlkit/widgets/common/sqlfilter.py:1048
msgid "Use this filter"
msgstr "Diesen Filter benutzen"

#: sqlkit/widgets/common/sqlfilter.py:1092
msgid "Click here to select an operator"
msgstr "Hier klicken um "

#: sqlkit/widgets/common/sqlfilter.py:1164
msgid "incomplete date format"
msgstr "Unvollständiges Datumsformat"

#: sqlkit/widgets/common/sqlwidget.py:358
#, python-format
msgid ""
"Sorry, problems connecting to remote db. Original error was: \n"
"\n"
"%s"
msgstr ""
"Sorry, Problem mit remote Datenbank Verdingung.  Orginaller Fehler war:\n"
"\n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:625
msgid "Hiding field is only supported for Tables"
msgstr "Verbergen eines Feldes ist nur für Tabellen unterstützt"

#: sqlkit/widgets/common/sqlwidget.py:792
msgid "Show all the differences that should be saved to database"
msgstr "Alle Unterschiede anzeigen die der Datenbank gespeichert werden sollten "

#: sqlkit/widgets/common/sqlwidget.py:802
msgid "Pending Differences"
msgstr "Anstehende Unterschiede"

#: sqlkit/widgets/common/sqlwidget.py:806
msgid "Save current record"
msgstr "Speichern des aktuellen Datensatzes"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Filter panel"
msgstr "Filter Panel"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Add filter panel"
msgstr "Filter Panel hinzufügen"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload"
msgstr "Neu Laden"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload from the database"
msgstr "Neu von der Datenbank laden"

#: sqlkit/widgets/common/sqlwidget.py:815
msgid "Go to next record"
msgstr "Gehe zum nächsten Datensatz"

#: sqlkit/widgets/common/sqlwidget.py:816
msgid "Go to previous record"
msgstr "Gehe zum vorhergehenden Datensatz"

#: sqlkit/widgets/common/sqlwidget.py:823
msgid "Inspect widgets"
msgstr "Überprüfe 'widgets'"

#: sqlkit/widgets/common/sqlwidget.py:990
msgid "Saved"
msgstr "Gesichert"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1020
#, python-format
msgid ""
"Error while writing to the database. \n"
"Original error was: \n"
"%s"
msgstr ""
"Fehler beim Schreiben in die Datenbank.\n"
"Orignaller Fehler war:\n"
"%s"

#. TIP: Error while saving into a table w/o permission
#. TIP: reloading data from the database
#: sqlkit/widgets/common/sqlwidget.py:1026
#: sqlkit/widgets/common/sqlwidget.py:1563
#, python-format
msgid ""
"A programming error was received from the backend:\n"
"%s"
msgstr ""
"Ein Programmier Fehler wurde vom Backend erhalten:\n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:1035
#, python-format
msgid "%s - Dirty objects: %s - New objects: %s"
msgstr "%s - Schmutzige Objekte: %s - Neue Objekte: %s"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1041
#, python-format
msgid ""
"Error while writing to the database: \n"
"are you 'saving as new' an already known record?\n"
"original error was: \n"
"%s"
msgstr ""
"Fehler beim schreiben in die Datenbank:\n"
"Speichern Sie \"als Neu\" einen Datensatz der schon existiert?\n"
"Original Fehler war:\n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:1186
msgid "Discarding new obj"
msgstr "Verwerfe neues Objekt"

#: sqlkit/widgets/common/sqlwidget.py:1223
#, python-format
msgid "Char %s is not accepted in numeric context"
msgstr "Char %s ist im numerischen Kontext nicht akzeptiert"

#: sqlkit/widgets/common/sqlwidget.py:1281
msgid "record validation"
msgstr "Datensatz Validierung"

#: sqlkit/widgets/common/sqlwidget.py:1503
msgid "Unsaved differences"
msgstr "Ungesicherte Differenzen"

#: sqlkit/widgets/common/sqlwidget.py:1741
msgid "Sorry, export is implemented only for table view"
msgstr "Sorry, Export ist nur für Tabellenansicht implementiert"

#: sqlkit/widgets/common/sqlwidget.py:2097
msgid "Property"
msgstr "Property"

#: sqlkit/widgets/common/sqlwidget.py:2098
msgid "Value"
msgstr "Wert"

#: sqlkit/widgets/common/sqlwidget.py:2184
msgid "Save unsaved data?"
msgstr "Sichere ungesicherte Daten"

#: sqlkit/widgets/common/sqlwidget.py:2187
msgid "Unsaved data"
msgstr "Ungesicherte Daten"

#: sqlkit/widgets/common/sqlwidget.py:2209
msgid "State"
msgstr "Zustand"

#: sqlkit/widgets/common/sqlwidget.py:2210
#: sqlkit/widgets/common/sqlwidget.py:2330
msgid "Field name"
msgstr "Feldname"

#: sqlkit/widgets/common/sqlwidget.py:2211
msgid "Original value"
msgstr "Orginal Wert"

#: sqlkit/widgets/common/sqlwidget.py:2212
msgid "Present value"
msgstr "Gegenwertiger Wert"

#: sqlkit/widgets/common/sqlwidget.py:2242
msgid "Modified"
msgstr "Geändert"

#: sqlkit/widgets/common/sqlwidget.py:2269 sqlkit/widgets/mask/mask.py:597
msgid "Deleted"
msgstr "Gelöscht"

#: sqlkit/widgets/common/sqlwidget.py:2279
msgid "New"
msgstr "Neu"

#: sqlkit/widgets/common/sqlwidget.py:2293
msgid "empty string: ''"
msgstr "Leere Zeichenfolge: ''"

#: sqlkit/widgets/common/sqlwidget.py:2295
msgid "NULL value"
msgstr "NULL Wert"

#: sqlkit/widgets/common/sqlwidget.py:2301
msgid ""
"Errors are present in the record. \n"
"Correct them now, to continue \n"
"or delete the record"
msgstr ""
"Der Datensatz enthält Fehler.\n"
"Entweder jetzt korrigieren, um weiter zu machen\n"
"oder den Datensatz löschen"

#: sqlkit/widgets/common/sqlwidget.py:2302
msgid "Validation errors"
msgstr "Validierungsfehlern"

#: sqlkit/widgets/common/sqlwidget.py:2331
msgid "Error"
msgstr "Fehler"

#: sqlkit/widgets/common/sqlwidget.py:2362
msgid ""
"You can continue or go back editing. \n"
"Read the following warnings to decide"
msgstr ""
"Sie können weiter machen oder gehen zurück zur Bearbeitung.\n"
"Lesen Sie den folgenden Warnhinweis um zu entscheiden "

#: sqlkit/widgets/common/sqlwidget.py:2363
msgid "Validation Warnings"
msgstr "Validierungswarnung"

#: sqlkit/widgets/mask/mask.py:258
msgid "Add new record"
msgstr "Neuen Datensatz hinzufügen"

#: sqlkit/widgets/mask/mask.py:262
msgid "Discard changes"
msgstr "Änderungen verwerfen"

#. TIP Modify menu entry in the mask to reread a single record from database
#: sqlkit/widgets/mask/mask.py:264
msgid "Refresh this record"
msgstr "Datensatz aktualisieren "

#: sqlkit/widgets/mask/mask.py:265
msgid "Reread this record from db"
msgstr "Datensatz neu von der Datenbank lesen"

#: sqlkit/widgets/mask/mask.py:268
msgid "Delete this record"
msgstr "Datensatz löschen"

#: sqlkit/widgets/mask/mask.py:342 sqlkit/widgets/table/table.py:908
msgid "Already at new record"
msgstr "Schon im \"Neuer Datensatz\" modus"

#: sqlkit/widgets/mask/mask.py:362
#, python-format
msgid "New record %s"
msgstr "Neuen Datensatz %s"

#. TIP message issued when a refresh is done on a deleted record
#: sqlkit/widgets/mask/mask.py:405
msgid "The record is no longer present in the database"
msgstr "Dieser Datensatz ist nicht mehr in der Datenbank"

#: sqlkit/widgets/mask/mask.py:473 sqlkit/widgets/table/table.py:838
msgid "Nothing to save"
msgstr "Nichts zu sichern"

#: sqlkit/widgets/mask/mask.py:523
#, python-format
msgid "Primary key (%s) didn't change. Refusing to save as new"
msgstr ""
"Primärschlüssel (%s) ist nicht geändert, kann nicht als Neu gespeichert "
"werden"

#: sqlkit/widgets/mask/mask.py:537
msgid "Do you want to copy all data to a new record?"
msgstr "Wollen Sie alle Daten in einen neuen Datensatz kopieren?"

#: sqlkit/widgets/mask/mask.py:585 sqlkit/widgets/table/table.py:982
#, python-format
msgid ""
"Delete this record?\n"
"(%s)"
msgstr ""
"Diesen Datensatz löschen?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:654
msgid "No record present"
msgstr "Kein Datensatz vorhanden"

#: sqlkit/widgets/mask/mask.py:677
msgid "Already last record"
msgstr "Schon beim letzen Datensatz"

#: sqlkit/widgets/mask/mask.py:679
msgid "Already first record"
msgstr "Schon beim ersten Datensatz"

#: sqlkit/widgets/mask/miniwidgets.py:695
#, python-format
msgid "A file with name '%s' already exists. Overwrite?"
msgstr "Eine Datei mit Namen \"%s\" existiert schon. Überschreiben?"

#: sqlkit/widgets/mask/miniwidgets.py:696
msgid "Upload name conflict"
msgstr "Namens konflikt beim hochladen"

#: sqlkit/widgets/mask/miniwidgets.py:888
#, python-format
msgid "'%s' may have an invalid value: try completion on that"
msgstr "'%s' kann einen ungültigen Wert haben: versuche Vervollständigung"

#: sqlkit/widgets/table/columns.py:208
msgid "Editing canceled. Restoring original value"
msgstr "Bearbeiten annuliert.  Wiederherstellung des Orginal Wertes"

#: sqlkit/widgets/table/columns.py:848
#, python-format
msgid "Add a filter on '%s'"
msgstr "Einen Filter für '%s' hinzufügen"

#: sqlkit/widgets/table/columns.py:858
msgid ""
"Sort on this column reloading from the database, you can used 's' to sort"
" locally"
msgstr ""
"Sortieren dieser Spalte und neu laden von der Datenbank, Sie können 's' "
"benutzen um Lokal zu sortieren"

#: sqlkit/widgets/table/columns.py:861
msgid "Sort on this column locally (w/o touching the database)"
msgstr "Sortierung der Spalte lokal (ohne die Datenbank zu berühren)"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:871
msgid "Hide this column"
msgstr "Diese Spalte verbergen"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:878
msgid "Create total"
msgstr "Summe anlegen"

#. TIP: column menu total
#: sqlkit/widgets/table/columns.py:888
#, python-format
msgid "Subtotal on %s"
msgstr "Zwischensumme auf %s"

#. TIP: column menu opt
#. TIP: modify menu entry
#: sqlkit/widgets/table/columns.py:910 sqlkit/widgets/table/columns.py:1293
#: sqlkit/widgets/table/table.py:287
msgid "Show field"
msgstr "Feld anzeigen"

#: sqlkit/widgets/table/columns.py:911 sqlkit/widgets/table/columns.py:1294
#: sqlkit/widgets/table/table.py:288
msgid "Hide field"
msgstr "Feld verbergen"

#: sqlkit/widgets/table/columns.py:938
msgid "day"
msgstr "Tag"

#: sqlkit/widgets/table/columns.py:938
msgid "week"
msgstr "Woche"

#: sqlkit/widgets/table/columns.py:938
msgid "month"
msgstr "Monat"

#: sqlkit/widgets/table/columns.py:939
msgid "quarter"
msgstr "Quartal"

#: sqlkit/widgets/table/columns.py:939
msgid "year"
msgstr "Jahr"

#: sqlkit/widgets/table/columns.py:942
#, python-format
msgid "Subtotals by %s"
msgstr "Zwischensumme auf %s"

#: sqlkit/widgets/table/columns.py:1267
msgid "Right click on this table to show the column again"
msgstr "Rechts auf dies Tabelle klicken um the Spalte wieder anzuzeigen"

#: sqlkit/widgets/table/columns.py:1287 sqlkit/widgets/table/table.py:301
msgid "Export"
msgstr "Exportieren"

#: sqlkit/widgets/table/columns.py:1287
msgid "Export these data into csv format"
msgstr "Exportiere diese Daten in das 'CSV' Format"

#: sqlkit/widgets/table/columns.py:1292
msgid "Adapt width of columns to data"
msgstr "Breite diese Spalte den Daten anpassen"

#: sqlkit/widgets/table/columns.py:1321
#, python-format
msgid "View '%s' in a Mask"
msgstr "'%s' in eine Maske anzeigen"

#: sqlkit/widgets/table/table.py:274
msgid "Duplicate"
msgstr "Duplikat"

#: sqlkit/widgets/table/table.py:275
msgid "Create a new row as a duplicate of this one"
msgstr "Eine neu"

#: sqlkit/widgets/table/table.py:277
msgid "New child row"
msgstr "Neue Unterzeile"

#: sqlkit/widgets/table/table.py:278
msgid "Create e new row as child of this one"
msgstr "Eine neue Zeile anlegen als Unterzeile von dieser Zeile"

#: sqlkit/widgets/table/table.py:292
msgid "View this record in a Mask"
msgstr "Diese Datensatz in eine Maske anzeigen"

#: sqlkit/widgets/table/table.py:293
msgid "View this ForeignKey in a Mask"
msgstr "Diesen Fremdschlüssel in eine Maske anzeigen"

#: sqlkit/widgets/table/table.py:294
msgid "Upload Image"
msgstr "Bild hochladen"

#: sqlkit/widgets/table/table.py:295
msgid "Upload File"
msgstr "Datei hochladen"

#: sqlkit/widgets/table/table.py:296
msgid "Delete File/Image"
msgstr "Datei/Bild löschen"

#: sqlkit/widgets/table/table.py:297
msgid "Show File/Image"
msgstr "Datei/Bild anzeigen"

#: sqlkit/widgets/table/table.py:298
msgid "Save File/Image"
msgstr "Datei/Bild sichern"

#: sqlkit/widgets/table/table.py:298
msgid "Save file locally"
msgstr "Datei lokal sichern"

#: sqlkit/widgets/table/table.py:528 sqlkit/widgets/table/table.py:542
#: sqlkit/widgets/table/table.py:554
msgid "Insertion of new records is disabled. Sorry"
msgstr "Neue Datensätze anlegen ist deaktiviert. Sorry."

#: sqlkit/widgets/table/table.py:558
msgid "Update of records is disabled. Sorry"
msgstr "Aktualisierung der Datensätze ist deaktiviert. Sorry"

#: sqlkit/widgets/table/table.py:570
msgid "Deletion of records is disabled. Sorry"
msgstr "Löschen von Datensätzen ist deaktiviert. Sorry"

#: sqlkit/widgets/table/table.py:583
msgid "browsing of new records is disabled. Sorry"
msgstr "'Browsing' neuer Datensätze ist deaktiviert. Sorry"

#: sqlkit/widgets/table/table.py:731
msgid "no current obj: maybe no record has yet been edited"
msgstr "Kein aktuelles Objekt, vielleicht wurde noch kein Datensatz bearbeitet."

#. TIP: when saving m2m, we delay till leader record will be saved
#: sqlkit/widgets/table/table.py:869
msgid "delaying saving when main record will be saved"
msgstr "Verzögerung der Speicherung bis Hauptdatensatz gespeichert wird"

#: sqlkit/widgets/table/table.py:879
msgid "Record has NOT been saved"
msgstr "Datensatz wurde nicht gespeichert"

#: sqlkit/widgets/table/table.py:978
msgid "No record selected"
msgstr "Kein Datensatz ist ausgewählt"

#: sqlkit/widgets/table/table.py:1137
msgid "Value is not valid, trying completion"
msgstr "Wert ist nicht gültig, versuche Vervollständigung"

#. TIP: check in input field length %s %s -> field_name length
#: sqlkit/widgets/table/table.py:1170
#, python-format
msgid "Input exceeded max %s length (%s)"
msgstr "Eingabe hat maximale %s Länge überschritten (%s)"

#: sqlkit/widgets/table/table.py:1284
msgid "Unsaved data prevent opening a Mask to show the (unsaved) record"
msgstr ""
"Nicht gespeicherte Daten, verhindert das öffnen der Maske um den "
"ungesicherten Datensatz anzuzeigen"

#: sqlkit/widgets/table/table.py:1407
#, python-format
msgid ""
"A file with name '%s' already exists.\n"
"Overwrite?"
msgstr ""
"Eine Datei mit Namen '%s' existiert schon.\n"
"Überschreiben?"

#: sqlkit/widgets/table/table.py:1408
msgid "Upload name duplication"
msgstr "Hochladen - Name duplikated"

#: sqlkit/widgets/table/table.py:1629
msgid "Export in csv file"
msgstr "Export in eine 'CSV' Datei"

#: sqlkit/widgets/table/table.py:1702
msgid "Csv files"
msgstr "'CSV' Dateien"

#: sqlkit/widgets/table/tablewidgets.py:34
#, python-format
msgid "No current obj for field '%s' in %s"
msgstr "Kein aktuelles Objekt for Feld '%s' in %s"
