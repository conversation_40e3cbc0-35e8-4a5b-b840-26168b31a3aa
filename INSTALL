Dependencies
============

Sqlkit depends on:

  * Python
  * Pygtk
  * sqlalchemy (>=0.5)
  * dateutils 
  * setuptools
  * the correct driver for your database of choice


Installation
============

Please read how to install in the web pacge of the installation tutorial
where tips for the different operative systems are collected:

  http://sqlkit.argolinux.org/misc/tutorial.html

as any python package, provided you have pygtk already installed, you can
install by just running:

  python setup.py install

it will install the library and a script called 'sqledit'. 

You can make a tour of the featueres from the 'demo' button of sqledit or
going to demo/sql subdir of the source package and running ./demo.py

Completion
==========

If you use sqledit from command line under linux you may find usefull the
sqledit.competion file that you should install under /etc/bash_completion.d

It lets you complete the arguments reading the nicks defined in .sqledit/nicks
