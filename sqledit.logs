Xlib:  extension "RANDR" missing on display ":1.0".
/app/sqlkit/sqlkit/layout/layout.py:471: GtkWarning: Unable to locate theme engine in module_path: "pixmap",
  self.builder.add_from_string(xml)
Traceback (most recent call last):
  File "/app/sqlkit/sqlkit/misc/table_browser.py", line 300, in activate_cb
    self.t = SqlWid(table, **options)
  File "/app/sqlkit/sqlkit/widgets/table/table.py", line 114, in __init__
    sqlwidget.SqlWidget.__init__(self, *args, **kwargs)
  File "/app/sqlkit/sqlkit/widgets/common/sqlwidget.py", line 323, in __init__
    self.mapper = self._get_mapper(mapper, table or tables, class_)
  File "/app/sqlkit/sqlkit/widgets/common/sqlwidget.py", line 427, in _get_mapper
    mapper = self.dbproxy.get_mapper(mapper, tables)
  File "/app/sqlkit/sqlkit/db/proxy.py", line 128, in get_mapper
    tables = [self.get_table(tbl) for tbl in tables]
  File "/app/sqlkit/sqlkit/db/proxy.py", line 102, in get_table
    return _autoload_table(name, self.metadata)
  File "/app/sqlkit/sqlkit/db/utils.py", line 125, in _autoload_table
    return Table(table_name, metadata, autoload=True)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/sql/schema.py", line 439, in __new__
    metadata._remove_table(name, schema)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/util/langhelpers.py", line 66, in __exit__
    compat.reraise(exc_type, exc_value, exc_tb)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/sql/schema.py", line 434, in __new__
    table._init(name, metadata, *args, **kw)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/sql/schema.py", line 514, in _init
    include_columns, _extend_on=_extend_on)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/sql/schema.py", line 540, in _autoload
    _extend_on=_extend_on
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 2045, in run_callable
    return conn.run_callable(callable_, *args, **kwargs)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 1534, in run_callable
    return callable_(self, *args, **kwargs)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/default.py", line 372, in reflecttable
    table, include_columns, exclude_columns, **opts)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/reflection.py", line 625, in reflecttable
    include_columns, exclude_columns, reflection_options)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/reflection.py", line 836, in _reflect_check_constraints
    constraints = self.get_check_constraints(table_name, schema)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/reflection.py", line 533, in get_check_constraints
    self.bind, table_name, schema, info_cache=self.info_cache, **kw)
  File "<string>", line 2, in get_check_constraints
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/reflection.py", line 54, in cache
    ret = fn(self, con, *args, **kw)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/dialects/postgresql/base.py", line 2911, in get_check_constraints
    c = connection.execute(sql.text(CHECK_SQL), table_oid=table_oid)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 945, in execute
    return meth(self, multiparams, params)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/sql/elements.py", line 263, in _execute_on_connection
    return connection._execute_clauseelement(self, multiparams, params)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 1053, in _execute_clauseelement
    compiled_sql, distilled_params
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 1189, in _execute_context
    context)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 1402, in _handle_dbapi_exception
    exc_info
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/util/compat.py", line 203, in raise_from_cause
    reraise(type(exception), exception, tb=exc_tb, cause=cause)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/base.py", line 1182, in _execute_context
    context)
  File "/usr/lib/python2.7/dist-packages/sqlalchemy/engine/default.py", line 470, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (psycopg2.ProgrammingError) ERRORE:  la colonna cons.consrc non esiste
LINE 4:                 cons.consrc as src
                        ^
HINT:  Forse intendevi referenziare la colonna "cons.conkey" o la colonna "cons.conbin".
 [SQL: "\n            SELECT\n                cons.conname as name,\n                cons.consrc as src\n            FROM\n                pg_catalog.pg_constraint cons\n            WHERE\n                cons.conrelid = %(table_oid)s AND\n                cons.contype = 'c'\n        "] [parameters: {'table_oid': 16396}]
