#!/usr/bin/python

"""
usage: %prog files [options] [[url|nick] table]
   -u, --url = URL: an url to open (eg *********************************
   -n, --nick = nick: a nick in ~/.sqleditdb
   -t, --table = table: open table 'table'
   -m, --mask: open a SqlMask (default is SqlTable)
   -T, --sqltable: open a SqlTable (default when -t is used)
   -d, --dev: open in 'dev' mode
   -D, --debug: print debug
   -g, --gtk-debug: use LogTheMethods
   -S, --sql=statement: execute statement (requires -t)
   -a, --all-tables: read all table on startup (very slow)
   -f, --field_list=fields: comma (or space) separated field list 
   -o, --order_by=fields: comma separated field list
   -c, --configure: open SqlMasq on _sqlkit_table or create it

"""

import sys, re, datetime
import os
import user
from ConfigParser import ConfigParser, NoSectionError
import pdb

#sys.path.insert(0,'/usr/src/sqlalchemy/lib/')
Dir = os.path.abspath(os.path.dirname(__file__))

from sqlkit import debug as dbg
import sqlkit
from sqlkit import layout
import sqlalchemy as sa
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
import gtk
from sqlkit.misc import optionparse, table_browser


class MissingNickDefinition(Exception): pass

def read_conf(nick):
    from copy import deepcopy

    global opts
    global lopts

    dbconf='.sqleditdb'
    dbconf = os.path.join(os.getenv('HOME'), dbconf)
    lopts = deepcopy(opts)

    def loop_on_opts(c, opts, nick=nick):
        """
        implements "copy" option in .sqleditdb
        """

        for key, val in c.items(nick):
            if key == 'copy':
                loop_on_opts(c, opts, nick=val)
        
        for key, val in c.items(nick):
            if key == 'copy':
                continue
            if getattr(lopts, key, None) is None:
                setattr(opts, key, val)
        

    if os.path.exists(dbconf):
        c = ConfigParser()
        c.read(dbconf)
        loop_on_opts(c, opts, nick)
    else:
        raise MissingNickDefinition
        
def sqlkit_model():
    lay = """
       name
       search_field
       format
       o2m=attributes -

    """
    global tbl
    try:
        from sqlkit.db.sqlkit_model import SqlkitTable, Base
        Base.metadata.bind = db.metadata.bind
        if not _has_table_pg_compatible(db.metadata.bind, '_sqlkit_table'):
            response = dialog_create_continue()
            if response == gtk.RESPONSE_CANCEL:
                sys.exit(0)
            else:    
                Base.metadata.create_all()
        tbl = SqlMask(Class=SqlkitTable, dbproxy=db, single=True, layout=lay)
    except Exception, e:
        print e
        raise
        
def dialog_create_continue():
    dialog = gtk.Dialog("Creating sqlkit tables", None,
                        gtk.DIALOG_MODAL | gtk.DIALOG_DESTROY_WITH_PARENT,
                        (gtk.STOCK_OK, gtk.RESPONSE_OK,
                         gtk.STOCK_CANCEL, gtk.RESPONSE_CANCEL))
    label = gtk.Label("No sqlkit tables are present: I'll proceed creating them")
    dialog.vbox.add(label)
    dialog.show_all()
    response = dialog.run()
    dialog.destroy()
    return response


def open_sqlwidget(table, single):
    global opts
    options ={'table' : table,
              'dev' : opts.dev,
              'single' : single,
              'nick' : table,
              'dbproxy' : db,
              'sql' : opts.sql,
              'field_list' : opts.field_list,
              'order_by' : opts.order_by,
           }
    if opts.mask:
        try:
            SqlMask(**options)
        except sqlkit.exc.MissingDescriptorField:
            pass

    else:
        try:
            SqlTable(**options)
        except sqlkit.exc.MissingDescriptorField:
            pass
            

###### Program

#table_name = None

opts, args = optionparse.parse(__doc__)
#print opts

if not args and not opts: optionparse.exit()

if opts.gtk_debug:
    dbg.trace_class(ok='SqlTable|SqlMask|Completion')
    but = 'set_fkey_descr|(lookup|set|get)_value|[el]ne_cb|match_func' + \
       '|cell_default_cb|cell_bool_cb|_lookup_value|is_fkey'
    dbg.trace_function(exclude=but)
    dbg.debug(True)
    lgm = dbg.ShowLogs(show=True)

import sqlkit
from sqlkit.widgets import SqlTable, SqlMask
from sqlkit.db import proxy
from sqlkit.db.utils import _has_table_pg_compatible
from sqlkit.layout import misc

if args:
    if re.search('://', args[0]):
        opts.url = args[0]
    else:
        opts.nick = args[0]
    
if len(args) >1:
    opts.table = args[1]
    
if opts.debug:
    dbg.debug(True)

if opts.nick:
    try:
        read_conf(opts.nick)
    except NoSectionError:
        print "No nick named '%s'" % opts.nick
        sys.exit(1)


try:
    db = proxy.DbProxy(engine=opts.url)
except ImportError, e:
    print e.message
    if e.message == 'No module named MySQLdb':
        print "You need to install mysql driver (python-mysqldb under debian)"
    sys.exit(1)
    
session = db.get_session()

#engine = sa.create_engine(opts.url)
#metadata = sa.MetaData(bind=opts.url)
#session = sessionmaker(bind=engine,transactional=True)()
#sa.global_connect(opts.url)
#engine = sa.default_metadata.engine


try:
     if opts.table:
         tables = re.split('[ ,]+', opts.table)
         for t in tables:
             if len(tables) > 1:
                 single = False
             else:
                 single = True
             open_sqlwidget(t, single)
     elif opts.configure:
         sqlkit_model()
         
     else:
         title = session.bind.url.database
         C = table_browser.TableBrowser(db, title=title, opts=opts, x='quit')    

except OperationalError, e:
    if opts.debug:
        raise e
    else:
        print e.orig
        sys.exit(1)
     
try:
    gtk.main()
except KeyboardInterrupt:
    pass
