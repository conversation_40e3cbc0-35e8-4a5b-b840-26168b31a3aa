Ideas list
==========

* Improve some widgets:

  + multiline editing/viewing under SqlTable  (important)
  + date/datetime/interval (minor)

* New layout formalism starting from studying the ReST very clean
  directives. Possibly adding possibility to use glade along with the actual
  (and preferred) layout system.

* Refactoring with the aim of simplifying. Need to add new tests where
  possible. Some parts are really old and need cleanup (5-10%), others have
  grown too big (the two main MegaWidgets).

* Analizing other similar projects to see what they can teach us. Projects
  to keep into consideration: dabo (wx based, site at http://dabodev.com/),
  camelot (qt based, site at http://www.python-camelot.com/)

* Now interactive filters only allow AND connectors. It's easy to allow OR
  instead but I'd like to understand how we can add more flexible intermixing
  of features. It may be worthwhile to see how Filemaker cope with that or
  Access. 

* Add a way to store queryies created dinamically by the user

* Add a plugin to work with Quickly

* Study wich are the bottleneck that slow it down with remote db server on a
  slow line.

* Add a way to inform user of all items deemed to deletetion when a record
  is deleted, i.e.: all records that would be deleted by the database or by
  sqlalchemy itself due to delete-orphan option.

