#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script per verificare che le correzioni per l'autoload di SQLAlchemy funzionino
"""

import sys
import os
import tempfile
import sqlalchemy
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String
from pkg_resources import parse_version

# Aggiungi il path di sqlkit
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'sqlkit'))

def test_autoload_compatibility():
    """Test che la funzione _autoload_table funzioni con diverse versioni di SQLAlchemy"""
    
    print(f"Testing with SQLAlchemy version: {sqlalchemy.__version__}")
    
    # Crea un database temporaneo in memoria
    engine = create_engine('sqlite:///:memory:')
    metadata = MetaData()
    
    # Crea una tabella di test
    test_table = Table('test_table', metadata,
                      Column('id', Integer, primary_key=True),
                      Column('name', String(50)))
    
    metadata.create_all(engine)
    
    # Inserisci alcuni dati di test
    with engine.connect() as conn:
        conn.execute(test_table.insert().values(id=1, name='Test 1'))
        conn.execute(test_table.insert().values(id=2, name='Test 2'))
        conn.commit()
    
    # Test della funzione _autoload_table
    try:
        from sqlkit.db.utils import _autoload_table
        
        # Crea una nuova metadata per il test di autoload
        new_metadata = MetaData()
        new_metadata.bind = engine
        
        # Test autoload
        autoloaded_table = _autoload_table('test_table', new_metadata)
        
        print(f"✓ Autoload successful!")
        print(f"  Table name: {autoloaded_table.name}")
        print(f"  Columns: {[col.name for col in autoloaded_table.columns]}")
        
        # Verifica che i dati siano accessibili
        with engine.connect() as conn:
            result = conn.execute(autoloaded_table.select())
            rows = result.fetchall()
            print(f"  Data rows: {len(rows)}")
            for row in rows:
                print(f"    {dict(row)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Autoload failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dbproxy_compatibility():
    """Test che DbProxy funzioni con le correzioni"""
    
    try:
        from sqlkit.db.proxy import DbProxy
        
        # Crea un database temporaneo
        engine = create_engine('sqlite:///:memory:')
        
        # Crea una tabella di test direttamente
        with engine.connect() as conn:
            conn.execute("""
                CREATE TABLE test_proxy_table (
                    id INTEGER PRIMARY KEY,
                    description TEXT
                )
            """)
            conn.execute("INSERT INTO test_proxy_table (id, description) VALUES (1, 'Test Description')")
            conn.commit()
        
        # Test DbProxy
        db = DbProxy(engine=engine)
        table = db.get_table('test_proxy_table')
        
        print(f"✓ DbProxy.get_table() successful!")
        print(f"  Table name: {table.name}")
        print(f"  Columns: {[col.name for col in table.columns]}")
        
        return True
        
    except Exception as e:
        print(f"✗ DbProxy test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("SQLKit Autoload Compatibility Test")
    print("=" * 60)
    
    success = True
    
    print("\n1. Testing _autoload_table function...")
    success &= test_autoload_compatibility()
    
    print("\n2. Testing DbProxy compatibility...")
    success &= test_dbproxy_compatibility()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All tests passed! SQLKit autoload fixes are working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check the errors above.")
        sys.exit(1)
