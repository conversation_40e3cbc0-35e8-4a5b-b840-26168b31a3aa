syntax:glob
Makefile
debian/python-sqlkit
build
dist
log
python-build-stamp-2.5
sqlkit.egg-info/
debian/files
doc/.build
sqlkit/widgets/table/multiline_renderer
debian/tmp
debian/python-sqlkit-hardy
debian/python-sqlkit-intrepid
debian/python-sqlkit-lenny
debian/sqlkit-doc
doc/sqlkit.2.pdf
pyinstaller
debian/python-sqlkit.postinst.debhelper
debian/python-sqlkit.prerm.debhelper
debian/python-sqlkit.substvars
debian/sqlkit-doc.substvars
demo/layout/mask.py
demo/layout/mask2.py
demo/sql/model/delcascade.py
demo/sql/model/rel.py
demo/sql/model/rel2.py
demo/sql/model/test2.py
demo/sql/model/test3.py
doc/sqlkit/minspect.rst
demo/sql/images/.thumbnail
demo/sql/model/db.sqlite
sqlkit/misc/utils2.py
sqlkit/misc/utils3.py
test.py
warnpyinstaller-sqledit.txt
warnsqledit.txt
