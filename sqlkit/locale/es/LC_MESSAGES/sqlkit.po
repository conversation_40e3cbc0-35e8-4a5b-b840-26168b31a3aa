# Spanish translations for .
# Copyright (C) 2009 ORGANIZATION
# This file is distributed under the same license as the  project.
# <AUTHOR> <EMAIL>, 2009.
#
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2009-07-02 12:34+0200\n"
"PO-Revision-Date: 2011-03-13 00:08+0100\n"
"Last-Translator: <PERSON> "
"<<EMAIL>>\n"
"Language-Team: Español <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 0.9.4\n"

#: bin/sqledit.py:87
msgid "Connection setup"
msgstr "Configuración de la conexión"

#: bin/sqledit.py:90
msgid ""
"\n"
"    You can indicate the database you want to connect to\n"
"    using an URI in the form as:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    or using password and username:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "
msgstr ""
"\n"
"    Puede indicar la base de datos a la que quiera conectarse\n"
"    usando una URI de la forma:\n"
"    <b>postgres://localhost/nombrebd</b>\n"
"    <b>sqlite:///nombrebd</b>\n"
"    o usando clave y usuario:\n"
"    <b>mysql://sandro:clave@host/nombrebd</b>\n"
"    "

#: bin/sqledit.py:117
msgid "Run Demo"
msgstr "Ejecutar Demo"

#: bin/sqledit.py:140
msgid ""
"Info on available backend:\n"
"postgres, mysql..."
msgstr ""
"Información sobre plataformas disponibles:\n"
"postgres, mysql..."

#: bin/sqledit.py:141
msgid "Sqledit manual page"
msgstr "Manual de Sqledit"

#: bin/sqledit.py:146
msgid ""
"Try continuosly to connect.\n"
"        Nice and usefull but may cause temporary blocks\n"
"        if you write an inexistent hostname\n"
"        "
msgstr ""
"Trate continuamente conectarse.\n"
"        Bueno y útil pero puede causar bloqueos temporales\n"
"        si usted escribe un nombre de host inexistentes\n"
"        "

#: bin/sqledit.py:160
msgid ""
"If you write a wrong hostname\n"
"the application may hang some seconds till the natural network timeout. \n"
"Uncheck the flag on the right ti disable this feature"
msgstr ""
"Si escribe un nombre de host errado\n"
"la aplicación puede colgarse algunos segundos hasta el timeout natural de"
" la red. \n"
"Des selecciones la bandera a la derecha eso inhabilitará esta propiedad"

#: bin/sqledit.py:187
#, python-format
msgid "Attempting to connect to %s"
msgstr "Intentando conectar a %s"

#: bin/sqledit.py:190
#, python-format
msgid "Connected to %s"
msgstr "Conectado a %s"

#: bin/sqledit.py:221
msgid "A complete demo of all the features of the sqlkit package"
msgstr "Una completa demostración de todas las cualidades del paquete sqlkit"

#: bin/sqledit.py:222
msgid ""
"The demo was not found, if you know where it is,\n"
"run it manually: python demo.py"
msgstr ""
"La demostración no fue encontrada, si conoce su localización,\n"
"ejecute esta manualmente: python demo.py"

#: sqlkit/exc.py:48
#, fuzzy, python-format
msgid "Field '%s' cannot be NULL"
msgstr "el campo %s no puede ser NULL"

#: sqlkit/exc.py:60
#, fuzzy, python-format
msgid "Field '%s' cannot have value %s"
msgstr "el campo %s no puede tener el valor %s"

#: sqlkit/fields.py:979
#, python-format
msgid "value is not Decimal nor string: %s"
msgstr "el valor no es Decimal ni cadena: %s"

#: sqlkit/fields.py:1055
#, python-format
msgid "value is not date nor string: %s"
msgstr "el valor no es fecha ni cadena: %s"

#: sqlkit/fields.py:1174 sqlkit/widgets/mask/miniwidgets.py:430
#, fuzzy, python-format
msgid "Wrong date format: %s"
msgstr "Formato de hora errado: %s"

#: sqlkit/fields.py:1184
msgid "Wrong time format"
msgstr "Formato di tempo errato"

#: sqlkit/fields.py:1336
msgid "Value cannot be NULL"
msgstr ""

#: sqlkit/fields.py:1340
#, fuzzy, python-format
msgid "Value '%s' is not accepted"
msgstr "El valor está disponible"

#: sqlkit/db/proxy.py:219
#, fuzzy, python-format
msgid "Table %s doesn't have a primary key, editing is not possible"
msgstr "La tabla %s no tiene una llave primaria, su edición no es posible"

#: sqlkit/layout/dateedit.py:190
msgid "Press Esc to close or double click a date to select it"
msgstr ""

#: sqlkit/layout/fk_entry.py:25
msgid "Find allowed values (Control-Enter/Shift-Enter)"
msgstr ""

#: sqlkit/layout/image_widget.py:142
msgid ""
"Right click on the image area\n"
" to upload an image"
msgstr ""

#: sqlkit/layout/image_widget.py:273 sqlkit/widgets/table/table.py:1395
msgid "Upload image"
msgstr ""

#: sqlkit/layout/image_widget.py:273
msgid "Upload or modify an image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete image"
msgstr ""

#: sqlkit/layout/image_widget.py:274
msgid "Delete the image"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:275
msgid "Open separate image viewer"
msgstr ""

#: sqlkit/layout/image_widget.py:276
msgid "Save image as"
msgstr ""

#: sqlkit/layout/image_widget.py:279
msgid "Show/Hide image name"
msgstr ""

#: sqlkit/layout/image_widget.py:317
msgid "upload image"
msgstr ""

#: sqlkit/misc/datetools.py:152
#, fuzzy
msgid "Incomplete date format"
msgstr "Formato de fecha errado %s"

#: sqlkit/misc/table_browser.py:135
msgid "Database"
msgstr "Base de Datos"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:136 sqlkit/misc/words.py:13
#: sqlkit/widgets/common/sqlwidget.py:795
msgid "Modify"
msgstr "Modificar"

#: sqlkit/misc/table_browser.py:137
msgid "Tool"
msgstr "Herramienta"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:138 sqlkit/misc/words.py:15
#: sqlkit/widgets/common/sqlwidget.py:798
msgid "Help"
msgstr "Ayuda"

#: sqlkit/misc/table_browser.py:141
msgid "Count records"
msgstr "Contar registros"

#: sqlkit/misc/table_browser.py:141
msgid "Count records in all tables"
msgstr "Contar registros en todas las tablas"

#: sqlkit/misc/table_browser.py:144
msgid ""
"Configure the fields: \n"
"labels, tooltip, completion, search field"
msgstr ""
"Configure los campos: \n"
"marcadores, tooltip, finalización, buscar campo"

#: sqlkit/misc/table_browser.py:147
msgid "Edit Sqlkit Fields"
msgstr "Edite campos de Sqlkit"

#: sqlkit/misc/table_browser.py:150
msgid "Primary Key"
msgstr "Llave primaria"

#: sqlkit/misc/table_browser.py:151
msgid "Show/Hide primary key if the key is numeric"
msgstr "Mostrar/Ocultar llave primaria si la llave es numerica"

#: sqlkit/misc/table_browser.py:152
msgid "Load data"
msgstr "Cargar datos"

#: sqlkit/misc/table_browser.py:153
msgid "Load the data as well"
msgstr "Cargar los datos también"

#: sqlkit/misc/table_browser.py:154
msgid "Blank"
msgstr "En blanco"

#: sqlkit/misc/table_browser.py:154
msgid "Cast blank into NULL"
msgstr "Cambiar blancos a NULL"

#: sqlkit/misc/table_browser.py:262
msgid "Mask"
msgstr "Mascara"

#: sqlkit/misc/table_browser.py:266
msgid "Table"
msgstr "Tabla"

#: sqlkit/misc/table_browser.py:272
msgid "Collapse row"
msgstr ""

#: sqlkit/misc/table_browser.py:276
msgid "Table reflection"
msgstr "Reflexión de la Tabla"

#: sqlkit/misc/table_browser.py:423
msgid "The name of the table we are customizing"
msgstr ""

#: sqlkit/misc/table_browser.py:424
#, python-format
msgid ""
"The best representation of a record \n"
"as a combination of fields, e.g.: %(title)s %(year)s"
msgstr ""

#: sqlkit/misc/table_browser.py:427
msgid "The field that will be searched for when completion is used"
msgstr ""

#. TIP: filter page of the filter panel
#: sqlkit/misc/words.py:7
msgid "filter"
msgstr "filtro"

#. TIP: output page of the filter panel
#: sqlkit/misc/words.py:9 sqlkit/widgets/common/sqlfilter.py:259
#: sqlkit/widgets/common/sqlfilter.py:306
msgid "output"
msgstr "salida"

#. TIP: menu entry
#: sqlkit/misc/words.py:11
msgid "File"
msgstr "Archivo"

#. TIP: menu entry
#: sqlkit/misc/words.py:17 sqlkit/widgets/common/sqlwidget.py:796
msgid "Go"
msgstr "Ir"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:19
msgid "opts"
msgstr "opts"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:21 sqlkit/widgets/common/sqlwidget.py:797
msgid "Tools"
msgstr "Herramientas"

#. TIP: serach mode:
#: sqlkit/misc/words.py:23
msgid "regexp"
msgstr "regexp"

#. TIP: search mode
#: sqlkit/misc/words.py:25
msgid "start"
msgstr "inicio"

#. TIP: count records in sqledit
#: sqlkit/misc/words.py:27
msgid "N.Rows"
msgstr "N. Filas"

#: sqlkit/misc/words.py:29
msgid "Indexes"
msgstr "Indices"

#: sqlkit/misc/words.py:30
msgid "Nullable"
msgstr "Nullable"

#: sqlkit/misc/words.py:31
msgid "Prim. Keys"
msgstr "Llave Prim."

#: sqlkit/misc/words.py:32
msgid "Default"
msgstr "Defecto"

#: sqlkit/misc/words.py:33
msgid "Foreign Key"
msgstr "Llave Foránea"

#: sqlkit/misc/words.py:34
msgid "filter_tables"
msgstr "filter_tables"

#. TIP: status bar
#: sqlkit/widgets/common/completion.py:285
#: sqlkit/widgets/common/completion.py:1124
#, python-format
msgid "search mode: %s"
msgstr "modo busca: %s"

#: sqlkit/widgets/common/completion.py:643
msgid "Show all info on this field, db type & Co."
msgstr "Muestre toda la información de estos campos, db type & Co."

#. TIP: menu enty in menu on right click on down arro in fkey completion widget
#: sqlkit/widgets/common/completion.py:649
msgid "Show possible values: regexp  - Ctrl-Enter"
msgstr "Muestre posibles valores: regexp - Ctrl-Enter"

#. TIP: yellow tip to menu entry in down arrow in completion widget
#: sqlkit/widgets/common/completion.py:653
msgid "Show all values in the db that match your string"
msgstr "Muestre todos los valores en la bd que concuerda con su cadena"

#: sqlkit/widgets/common/completion.py:658
msgid "Show possible values, starting - Shift-Enter"
msgstr "Muestre posibles valores, iniciando - Shift-Enter"

#: sqlkit/widgets/common/completion.py:661
msgid "Show all values in the database starting with your string"
msgstr "Muestre todos los valores en la base de datos comenzando con su cadena"

#: sqlkit/widgets/common/completion.py:667
msgid "Edit the referenced table in 'table' mode"
msgstr "Edite la tabla en referencia en el modo 'tabla'"

#: sqlkit/widgets/common/completion.py:669
msgid "Edit the table from which admitted values are taken in 'table' mode"
msgstr "Edite la tabla para la cual ha admitido valores tomados en el modo 'tabla'"

#: sqlkit/widgets/common/completion.py:674
msgid "Edit the referenced table in 'mask' mode"
msgstr "Edite la tabla en referencia en el modo 'mascara'"

#: sqlkit/widgets/common/completion.py:676
msgid "Edit the table from which admitted values are taken in 'mask' mode"
msgstr ""
"Edite la tabla para la cual ha admitido valores tomados en el modo "
"'mascara'"

#: sqlkit/widgets/common/completion.py:783
msgid "Good match"
msgstr "Buena coincidencia"

#: sqlkit/widgets/common/completion.py:792
#: sqlkit/widgets/common/completion.py:798
msgid "No exact match, trying regexp completion"
msgstr "No hay coincidencia exacta, tratando finalización regexp"

#: sqlkit/widgets/common/dialogs.py:26 sqlkit/widgets/common/dialogs.py:94
#: sqlkit/widgets/table/table.py:1703
#, fuzzy
msgid "All files"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:100
msgid "Images"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:106
msgid "Documents"
msgstr ""

#. TIP: possible new name for an uploaded file
#: sqlkit/widgets/common/dialogs.py:123
msgid "Preferred new filename:"
msgstr ""

#: sqlkit/widgets/common/dialogs.py:124
msgid "If not empty, the file will be uploaded with this new name"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:107
#: sqlkit/widgets/common/sqlfilter.py:110
msgid "Match as regexp"
msgstr "Coincidencia como regexp"

#: sqlkit/widgets/common/sqlfilter.py:108
msgid "Match as LIKE, \"%\" automatically added"
msgstr "Coincide como LIKE, \"%\" adicionado automáticamente"

#: sqlkit/widgets/common/sqlfilter.py:109
msgid "Match as LIKE case insensitive, \"%\" automatically added"
msgstr "Coincidencia a LIKE insensible al caso, \"%\" adicionado automáticamente "

#: sqlkit/widgets/common/sqlfilter.py:111
msgid "Match as regexp, case insensitive"
msgstr "Coincidencia como regexp, insensible al caso"

#: sqlkit/widgets/common/sqlfilter.py:112
msgid "Negation of match as regexp"
msgstr "Negación de coincidencia como regexp"

#: sqlkit/widgets/common/sqlfilter.py:113
msgid "Negation of match case insensitive"
msgstr "Negación de coincidencia insensible al caso"

#: sqlkit/widgets/common/sqlfilter.py:114
msgid "Equal"
msgstr "Igual"

#: sqlkit/widgets/common/sqlfilter.py:115
msgid "Not equal"
msgstr "No igual"

#: sqlkit/widgets/common/sqlfilter.py:116
msgid "Greater then (after than)"
msgstr "Mayor que (después de)"

#: sqlkit/widgets/common/sqlfilter.py:117
msgid "Greater or equal"
msgstr "Mayor o Igual"

#: sqlkit/widgets/common/sqlfilter.py:118
msgid "Less than (before then)"
msgstr "Menor que (antes de)"

#: sqlkit/widgets/common/sqlfilter.py:119
msgid "Less than or equal"
msgstr "Menos que o igual"

#: sqlkit/widgets/common/sqlfilter.py:120
msgid "LIKE: a \"%\" means any char - case sensitive"
msgstr "LIKE: un \"%\" significa cualquier carácter - sensible al caso"

#: sqlkit/widgets/common/sqlfilter.py:121
msgid "Negation of LIKE"
msgstr "Negación de LIKE"

#: sqlkit/widgets/common/sqlfilter.py:122
msgid "As LIKE but case insensitive"
msgstr "Como LIKE pero insensible al caso"

#: sqlkit/widgets/common/sqlfilter.py:123
msgid "Negation of ILIKE"
msgstr "Negación de ILIKE"

#: sqlkit/widgets/common/sqlfilter.py:124
msgid "The boolean is True"
msgstr "El booleano es True"

#: sqlkit/widgets/common/sqlfilter.py:125
msgid "The boolean is False"
msgstr "El booleano es False"

#: sqlkit/widgets/common/sqlfilter.py:126
#, fuzzy
msgid "The boolean is not True"
msgstr "El valor no está disponible"

#: sqlkit/widgets/common/sqlfilter.py:127
msgid "The boolean is not False"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:128
#, fuzzy
msgid "The value is not set"
msgstr "El valor está disponible"

#: sqlkit/widgets/common/sqlfilter.py:129
msgid "The value is set"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:130
msgid "ID equality (don't follow foreign table)"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:221
msgid "Filter Panel"
msgstr "Panel de Filtro"

#: sqlkit/widgets/common/sqlfilter.py:230
msgid "Add filters for your query"
msgstr "Adicione filtros para su consulta"

#: sqlkit/widgets/common/sqlfilter.py:232
msgid "Result page for your query"
msgstr "Página resultado para su consulta"

#: sqlkit/widgets/common/sqlfilter.py:269
#, fuzzy
msgid "Filter actions"
msgstr "Panel de Filtros"

#: sqlkit/widgets/common/sqlfilter.py:270
msgid "Reload from db"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:271
#, fuzzy
msgid "Close the panel"
msgstr "Panel de Filtros"

#: sqlkit/widgets/common/sqlfilter.py:274
#, fuzzy
msgid "Go to filter panel"
msgstr "Panel de Filtros"

#: sqlkit/widgets/common/sqlfilter.py:276
msgid "Go to output panel"
msgstr ""

#. TIP: status bar message
#: sqlkit/widgets/common/sqlfilter.py:479 sqlkit/widgets/table/table.py:762
#: sqlkit/widgets/table/table.py:1039
#, python-format
msgid "Total N. of records: %s"
msgstr "Total N. de registros: %s"

#: sqlkit/widgets/common/sqlfilter.py:801
#, python-format
msgid "value '%s' cannot be used for field '%s'"
msgstr ""

#: sqlkit/widgets/common/sqlfilter.py:868
#, python-format
msgid ""
"value '%s' does not seem a valid date and cannot be transformed into a "
"date"
msgstr ""
"valor '%s' no parece una fecha valida y no puede ser transformada en una "
"fecha"

#. TIP: appears in the menu in the filter panel to add a second entry of the
#. same field
#: sqlkit/widgets/common/sqlfilter.py:1042
#, python-format
msgid "Add a new filter on this field '%s'"
msgstr "Adicione un nuevo filtro al campo '%s'"

#: sqlkit/widgets/common/sqlfilter.py:1048
msgid "Use this filter"
msgstr "Use este filtro"

#: sqlkit/widgets/common/sqlfilter.py:1092
msgid "Click here to select an operator"
msgstr "Presione aquí para seleccionar un operador"

#: sqlkit/widgets/common/sqlfilter.py:1164
msgid "incomplete date format"
msgstr "formato data incompleto"

#: sqlkit/widgets/common/sqlwidget.py:358
#, fuzzy, python-format
msgid ""
"Sorry, problems connecting to remote db. Original error was: \n"
"\n"
"%s"
msgstr ""
"Error mientras escribe la base de datos: \n"
"̉¿esta 'salvando como nuevo' un registro conocido?\n"
"el error original fue: \n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:625
msgid "Hiding field is only supported for Tables"
msgstr "Ocultar campos solo es soportado para Tablas"

#: sqlkit/widgets/common/sqlwidget.py:792
msgid "Show all the differences that should be saved to database"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:802
msgid "Pending Differences"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:806
#, fuzzy
msgid "Save current record"
msgstr "Contar registros"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Filter panel"
msgstr "Panel de Filtros"

#: sqlkit/widgets/common/sqlwidget.py:810
#, fuzzy
msgid "Add filter panel"
msgstr "Panel de Filtros"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload"
msgstr "Recargar"

#: sqlkit/widgets/common/sqlwidget.py:811
#, fuzzy
msgid "Reload from the database"
msgstr "Cargar los datos también"

#: sqlkit/widgets/common/sqlwidget.py:815
#, fuzzy
msgid "Go to next record"
msgstr "Contar registros"

#: sqlkit/widgets/common/sqlwidget.py:816
msgid "Go to previous record"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:823
msgid "Inspect widgets"
msgstr "Inspeccionar widgets"

#: sqlkit/widgets/common/sqlwidget.py:990
msgid "Saved"
msgstr "Salvado"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1020
#, fuzzy, python-format
msgid ""
"Error while writing to the database. \n"
"Original error was: \n"
"%s"
msgstr ""
"Error mientras escribe la base de datos: \n"
"̉¿esta 'salvando como nuevo' un registro conocido?\n"
"el error original fue: \n"
"%s"

#. TIP: Error while saving into a table w/o permission
#. TIP: reloading data from the database
#: sqlkit/widgets/common/sqlwidget.py:1026
#: sqlkit/widgets/common/sqlwidget.py:1563
#, python-format
msgid ""
"A programming error was received from the backend:\n"
"%s"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1035
#, python-format
msgid "%s - Dirty objects: %s - New objects: %s"
msgstr "%s - Objetos sucios: %s - Nuevos objetos: %s"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1041
#, python-format
msgid ""
"Error while writing to the database: \n"
"are you 'saving as new' an already known record?\n"
"original error was: \n"
"%s"
msgstr ""
"Error mientras escribe la base de datos: \n"
"̉¿esta 'salvando como nuevo' un registro conocido?\n"
"el error original fue: \n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:1186
msgid "Discarding new obj"
msgstr "Eliminar nuevo obj"

#: sqlkit/widgets/common/sqlwidget.py:1223
#, python-format
msgid "Char %s is not accepted in numeric context"
msgstr "Carácter %s no es aceptado en el contexto numérico"

#: sqlkit/widgets/common/sqlwidget.py:1281
msgid "record validation"
msgstr "validación de registro"

#: sqlkit/widgets/common/sqlwidget.py:1503
msgid "Unsaved differences"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:1741
msgid "Sorry, export is implemented only for table view"
msgstr "Disculpe, exportar está implementado solo para tablas vista"

#: sqlkit/widgets/common/sqlwidget.py:2097
msgid "Property"
msgstr "Propiedad"

#: sqlkit/widgets/common/sqlwidget.py:2098
msgid "Value"
msgstr "Valor"

#: sqlkit/widgets/common/sqlwidget.py:2184
msgid "Save unsaved data?"
msgstr "¿Salva dato no salvado?"

#: sqlkit/widgets/common/sqlwidget.py:2187
msgid "Unsaved data"
msgstr "Dato no salvado"

#: sqlkit/widgets/common/sqlwidget.py:2209
msgid "State"
msgstr "Estado"

#: sqlkit/widgets/common/sqlwidget.py:2210
#: sqlkit/widgets/common/sqlwidget.py:2330
msgid "Field name"
msgstr "Nombre del campo"

#: sqlkit/widgets/common/sqlwidget.py:2211
msgid "Original value"
msgstr "Valor original"

#: sqlkit/widgets/common/sqlwidget.py:2212
msgid "Present value"
msgstr "Valor presente"

#: sqlkit/widgets/common/sqlwidget.py:2242
msgid "Modified"
msgstr "Modificado"

#: sqlkit/widgets/common/sqlwidget.py:2269 sqlkit/widgets/mask/mask.py:597
msgid "Deleted"
msgstr "Borrado"

#: sqlkit/widgets/common/sqlwidget.py:2279
msgid "New"
msgstr "Nuevo"

#: sqlkit/widgets/common/sqlwidget.py:2293
msgid "empty string: ''"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2295
msgid "NULL value"
msgstr ""

#: sqlkit/widgets/common/sqlwidget.py:2301
msgid ""
"Errors are present in the record. \n"
"Correct them now, to continue \n"
"or delete the record"
msgstr ""
"Errores presentes en el registro. \n"
"Corrija ahora, para continuar \n"
"o borre el registro"

#: sqlkit/widgets/common/sqlwidget.py:2302
msgid "Validation errors"
msgstr "Validación de errores"

#: sqlkit/widgets/common/sqlwidget.py:2331
msgid "Error"
msgstr "Error"

#: sqlkit/widgets/common/sqlwidget.py:2362
msgid ""
"You can continue or go back editing. \n"
"Read the following warnings to decide"
msgstr ""
"Puede continuar o regresar a editar. \n"
"Lea la siguiente advertencia para decidir"

#: sqlkit/widgets/common/sqlwidget.py:2363
msgid "Validation Warnings"
msgstr "Validación de advertencias"

#: sqlkit/widgets/mask/mask.py:258
#, fuzzy
msgid "Add new record"
msgstr "Ya está en el nuevo registro"

#: sqlkit/widgets/mask/mask.py:262
msgid "Discard changes"
msgstr ""

#. TIP Modify menu entry in the mask to reread a single record from database
#: sqlkit/widgets/mask/mask.py:264
#, fuzzy
msgid "Refresh this record"
msgstr ""
"¿Borra este registro?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:265
#, fuzzy
msgid "Reread this record from db"
msgstr ""
"¿Borra este registro?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:268
#, fuzzy
msgid "Delete this record"
msgstr ""
"¿Borra este registro?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:342 sqlkit/widgets/table/table.py:908
msgid "Already at new record"
msgstr "Ya está en el nuevo registro"

#: sqlkit/widgets/mask/mask.py:362
#, python-format
msgid "New record %s"
msgstr "Nuevo registro %s"

#. TIP message issued when a refresh is done on a deleted record
#: sqlkit/widgets/mask/mask.py:405
msgid "The record is no longer present in the database"
msgstr ""

#: sqlkit/widgets/mask/mask.py:473 sqlkit/widgets/table/table.py:838
msgid "Nothing to save"
msgstr "Nada para salvar"

#: sqlkit/widgets/mask/mask.py:523
#, python-format
msgid "Primary key (%s) didn't change. Refusing to save as new"
msgstr "Llave primaria (%s) no cambia. Se niega salvarla como nueva"

#: sqlkit/widgets/mask/mask.py:537
msgid "Do you want to copy all data to a new record?"
msgstr ""

#: sqlkit/widgets/mask/mask.py:585 sqlkit/widgets/table/table.py:982
#, python-format
msgid ""
"Delete this record?\n"
"(%s)"
msgstr ""
"¿Borra este registro?\n"
"(%s)"

#: sqlkit/widgets/mask/mask.py:654
msgid "No record present"
msgstr "No hay registro presente"

#: sqlkit/widgets/mask/mask.py:677
msgid "Already last record"
msgstr "Último Registro"

#: sqlkit/widgets/mask/mask.py:679
msgid "Already first record"
msgstr "Primer registro"

#: sqlkit/widgets/mask/miniwidgets.py:695
#, python-format
msgid "A file with name '%s' already exists. Overwrite?"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:696
msgid "Upload name conflict"
msgstr ""

#: sqlkit/widgets/mask/miniwidgets.py:888
#, python-format
msgid "'%s' may have an invalid value: try completion on that"
msgstr "'%s' puede tener un valor inválido: trate de completarlo"

#: sqlkit/widgets/table/columns.py:208
msgid "Editing canceled. Restoring original value"
msgstr "Edición cancelada. Restaurando el valor original"

#: sqlkit/widgets/table/columns.py:848
#, python-format
msgid "Add a filter on '%s'"
msgstr "Adicionar un filtro a '%s' "

#: sqlkit/widgets/table/columns.py:858
msgid ""
"Sort on this column reloading from the database, you can used 's' to sort"
" locally"
msgstr ""

#: sqlkit/widgets/table/columns.py:861
msgid "Sort on this column locally (w/o touching the database)"
msgstr ""

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:871
msgid "Hide this column"
msgstr "Ocultar esta columna"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:878
msgid "Create total"
msgstr "Crear total"

#. TIP: column menu total
#: sqlkit/widgets/table/columns.py:888
#, python-format
msgid "Subtotal on %s"
msgstr "Subtotal en %s"

#. TIP: column menu opt
#. TIP: modify menu entry
#: sqlkit/widgets/table/columns.py:910 sqlkit/widgets/table/columns.py:1293
#: sqlkit/widgets/table/table.py:287
msgid "Show field"
msgstr "Mostrar campo"

#: sqlkit/widgets/table/columns.py:911 sqlkit/widgets/table/columns.py:1294
#: sqlkit/widgets/table/table.py:288
msgid "Hide field"
msgstr "Ocultar campo"

#: sqlkit/widgets/table/columns.py:938
msgid "day"
msgstr "día"

#: sqlkit/widgets/table/columns.py:938
msgid "week"
msgstr "semana"

#: sqlkit/widgets/table/columns.py:938
msgid "month"
msgstr "mes"

#: sqlkit/widgets/table/columns.py:939
msgid "quarter"
msgstr "trimestre"

#: sqlkit/widgets/table/columns.py:939
msgid "year"
msgstr "año"

#: sqlkit/widgets/table/columns.py:942
#, python-format
msgid "Subtotals by %s"
msgstr "Subtotal por %s"

#: sqlkit/widgets/table/columns.py:1267
msgid "Right click on this table to show the column again"
msgstr ""

#: sqlkit/widgets/table/columns.py:1287 sqlkit/widgets/table/table.py:301
msgid "Export"
msgstr "Exportar"

#: sqlkit/widgets/table/columns.py:1287
msgid "Export these data into csv format"
msgstr ""

#: sqlkit/widgets/table/columns.py:1292
msgid "Adapt width of columns to data"
msgstr ""

#: sqlkit/widgets/table/columns.py:1321
#, python-format
msgid "View '%s' in a Mask"
msgstr "Ver '%s' en una Máscara"

#: sqlkit/widgets/table/table.py:274
msgid "Duplicate"
msgstr ""

#: sqlkit/widgets/table/table.py:275
msgid "Create a new row as a duplicate of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:277
msgid "New child row"
msgstr ""

#: sqlkit/widgets/table/table.py:278
msgid "Create e new row as child of this one"
msgstr ""

#: sqlkit/widgets/table/table.py:292
msgid "View this record in a Mask"
msgstr "Ver este registro en una Máscara"

#: sqlkit/widgets/table/table.py:293
msgid "View this ForeignKey in a Mask"
msgstr "Ver esta llave foránea en una Máscara"

#: sqlkit/widgets/table/table.py:294
msgid "Upload Image"
msgstr ""

#: sqlkit/widgets/table/table.py:295
msgid "Upload File"
msgstr ""

#: sqlkit/widgets/table/table.py:296
msgid "Delete File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:297
msgid "Show File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save File/Image"
msgstr ""

#: sqlkit/widgets/table/table.py:298
msgid "Save file locally"
msgstr ""

#: sqlkit/widgets/table/table.py:528 sqlkit/widgets/table/table.py:542
#: sqlkit/widgets/table/table.py:554
msgid "Insertion of new records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:558
msgid "Update of records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:570
msgid "Deletion of records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:583
msgid "browsing of new records is disabled. Sorry"
msgstr ""

#: sqlkit/widgets/table/table.py:731
msgid "no current obj: maybe no record has yet been edited"
msgstr "no hay obj actual: tal vez ningún registro ha sido editado aún"

#. TIP: when saving m2m, we delay till leader record will be saved
#: sqlkit/widgets/table/table.py:869
msgid "delaying saving when main record will be saved"
msgstr "retardando salvar mientras el registro principal sea salvado"

#: sqlkit/widgets/table/table.py:879
msgid "Record has NOT been saved"
msgstr "Registro NO ha sido salvado"

#: sqlkit/widgets/table/table.py:978
msgid "No record selected"
msgstr ""

#: sqlkit/widgets/table/table.py:1137
msgid "Value is not valid, trying completion"
msgstr "El valor no es valido, trate de completar"

#. TIP: check in input field length %s %s -> field_name length
#: sqlkit/widgets/table/table.py:1170
#, python-format
msgid "Input exceeded max %s length (%s)"
msgstr "Entrada excede el máximo %s tamaño (%s)"

#: sqlkit/widgets/table/table.py:1284
msgid "Unsaved data prevent opening a Mask to show the (unsaved) record"
msgstr ""

#: sqlkit/widgets/table/table.py:1407
#, python-format
msgid ""
"A file with name '%s' already exists.\n"
"Overwrite?"
msgstr ""

#: sqlkit/widgets/table/table.py:1408
msgid "Upload name duplication"
msgstr ""

#: sqlkit/widgets/table/table.py:1629
msgid "Export in csv file"
msgstr "Exportar a archivo csv"

#: sqlkit/widgets/table/table.py:1702
#, fuzzy
msgid "Csv files"
msgstr ""

#: sqlkit/widgets/table/tablewidgets.py:34
#, python-format
msgid "No current obj for field '%s' in %s"
msgstr "No hay objeto actual para el campo '%s' en %s"

