# -*- mode: python -*-
# this is a trial for an alternative syntax inspired by .rst syntax
# it's definitely simpler and cleaner

LAYOUT_CLIENTE = """
             {|   {   cognome  ro=created
                      nome     architetto_id   }  m2m=progettisti:3:username }
        {N

           { %generalita

              {  titolo_id  tel:150 incompleto } - - -
    
              {|>.tel/mail
                    cell       email
                    tel_uff    email2
                    fax        { piva:11    cf:16 } -
                    note_tel:.30 - - -
              {>.secondo_riferimento

                    nome_partner       cognome_partner
                    tel_partner        fax_partner
                    cell_partner       email_partner
                    tel_uff_partner    email2_partner

              } - - - 

              } -  - -

              {|>>.statistiche
                    
                   { {|  fornitore     partner         concorrente  
                         cliente       preventivo      contatto }
                     {  mandato_da_id }

                     {T.2m  m2m=provenienza:3     m2m=motivo:3  }

                   }  {| vip              influenzatore   tu
                       invito_cartaceo  auguri     
                       anni             esclusione_posta - - - - 
                       motivo_esclusione - - - -
                       categoria_id - - }

                     {= L=note:< @ TXS==note }      -  


              } - - - 

              o2m=indirizzi:4 - - -
              } 
            { %contatti
               m2o=contatti
            }

#             { %commissioni
         
#             }

            { %preventivi
         
            }

            { %jobs
              m2m=jobs
            }

               } -

        """

NEW_LAYOUT = """

|
     cognome  ro=created         m2m=progettisti
     nome     architetto_id      ^

N
    <generalita>

          titolo_id   tel:150    incompleto 

          |>.tel/mail

               cell       email      -
               tel_uff    email2     -
               fax        piva:11    cf:16 
               note_tel:.30 - - -

          >.secondo_riferimento

               nome_partner       cognome_partner
               tel_partner        fax_partner
               cell_partner       email_partner
               tel_uff_partner    email2_partner

          |>>.statistiche


                **

                    |  fornitore     partner         concorrente  
                    |  cliente       preventivo      contatto 

                    mandato_da_id         -
                    m2m=provenienza:3     m2m=motivo:3  

                **

                    | vip              influenzatore   tu
                    | invito_cartaceo  auguri     
                    | anni             esclusione_posta - - - - 
                    | motivo_esclusione - - -
                    | categoria_id 

             =

               L=note:<
               TXS==note 


          o2m=indirizzi:4 - - -

    <contatti>

         m2o=contatti

    # <commissioni>

    <preventivi>

    <jobs>

       m2m=jobs


""""
