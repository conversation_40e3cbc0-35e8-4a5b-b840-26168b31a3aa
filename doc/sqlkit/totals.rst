.. _totals:

========
 Totals
========

Numeric columns of a Table have the option to show totals and
subtotals. Subtotals are determined by brake fields/functions. This may be
done interactively from the column menu or from code. The following example
is very stupid but... you get the feeling!::

    t = SqlTable('movie', dbproxy=db, order_by='director_id')
    t.totals.add_break('director_id')
    t.totals.add_total('year')


.. image:: ../img/totals.png
    
  
Personalization and colors
==========================

Totals are generated by a class ``sqlkit.widgets.table.totals.Totals`` that
can be inherited and modified to set different total behavior.

The colors are defined in a class in the same module ``TotalObj`` whose
method ``set_value_and_colors`` can be used to personalize colors and markup
of the cell.

Dates
=====

Since date breaks are probably very common a function makes it easy to
brake on dates.

Totals... with no totals!
=========================

If you only need subtotals and not totals you can prevent totals using
option ``hide_total`` when declaring the column to sum.

Signals
=======

:computed: this signal is emitted when the total is computed. The signature
           of the callback function is:
       
      .. function:: computed_cb(total):
      
         :param sqlwidget: the total instance that emitted the signal

API
---

.. automodule:: sqlkit.widgets.table.totals

   
