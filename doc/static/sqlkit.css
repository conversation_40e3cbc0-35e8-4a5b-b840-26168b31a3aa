/**
 * Alternate Sphinx design
 * Originally created by <PERSON><PERSON> for Werkzeug, adapted by <PERSON>.
 * modified by <PERSON><PERSON> per ReteIsi
 */
html, body {
    height: 98%
    }
#wrap {
    background: none repeat scroll 0 0 #FFFFFF;
    margin: 0px auto;
    max-width: 1024px;
    min-width: 800px;
    position: relative;
    text-align: left;
    min-height: 100%;
    margin-top: -10px
    }
/* #wrap { */
/*     min-height: 100% */
/*     } */
#document {
    overflow: auto;
    padding-bottom: 50px
    }
/* must be same height as the footer */
#footer {
    position: relative;
    margin-top: -50px;
    /* negative value of footer height */
    height: 50px;
    clear: both
    }
/*Opera Fix*/
body:before {
    content: "";
    height: 100%;
    float: left;
    width: 0;
    margin-top: -32767px
    }
body {
    font-family: "Lucida Grande", "Lucida Sans Unicode", "Geneva", "Verdana", sans-serif;
    font-size: 13px;
    letter-spacing: -0.01em;
    line-height: 130%;
    text-align: center;
    /*background-color: #AFC1C4; */
    /*     background-color: #e9d56c; */
    background-color: #fff;
    color: #333333;
    padding: 0;
    /*     border: 1px solid #aaa; */
    /*     margin: 0px 80px 0px 80px; */
    /*     min-width: 740px */
    }
.logo {
    margin: 10px 10px 20px 10px
    }
section h1 {
    background-color: #FDFAF3;
    font-size: 180%;
    font-weight: bold
    }
.section h2, h2 {
    font-size: 150%;
    font-weight: normal
    }
.section h1, .section h2, h1, h2 {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-color: #E2DCC8;
    border-style: solid;
    border-width: 0pt 0pt 1px
    }
.section h1, .section h2, .section h3, h1, h2, h3 {
    color: #940000 ;
    font-weight: bold;
    }
a {
    color: #ca7900;
    text-decoration: none
    }
a:hover {
    color: #2491CF
    }
pre {
    font-family: "Consolas", "Deja Vu Sans Mono", "Bitstream Vera Sans Mono", monospace;
    font-size: 0.95em;
    letter-spacing: 0.015em;
    padding: 0.5em;
    border: 1px solid #ccc;
    background-color: #f8f8f8
    }
td.linenos pre {
    padding: 0;
    border: 0;
    background-color: transparent;
    color: #aaa
    }
table.highlighttable {
    margin-left: 0.5em
    }
table.highlighttable td {
    padding: 0 0.5em 0 0.5em
    }
cite, code, tt {
    font-family: "Consolas", "Deja Vu Sans Mono", "Bitstream Vera Sans Mono", monospace;
    font-size: 1.05em;
    letter-spacing: 0.01em;
    font-style: normal
    }
hr {
    border: 1px solid #abc;
    margin: 2em
    }
tt {
    /*     background-color: #f2f2f2; */
    /*     border-bottom: 1px solid #ddd; */
    color: #222;
    font-weight: bold
    }
tt.descname {
    background-color: transparent;
    font-weight: bold;
    font-size: 1.2em;
    color: #5454d8;
    border: 0
    }
tt.descclassname {
    background-color: transparent;
    border: 0
    }
tt.xref {
    background-color: transparent;
    font-weight: bold;
    border: 0
    }
a tt {
    background-color: transparent;
    font-weight: bold;
    border: 0;
    color: #ca7900
    }
a tt:hover {
    color: #2491CF
    }
.field-list ul {
    margin: 0;
    padding-left: 1em
    }
.field-list p {
    margin: 0
    }
dl {
    margin-bottom: 15px
    }
dd p {
    margin-top: 0px
    }
dd ul, dd table {
    margin-bottom: 10px
    }
dd {
    margin-top: 3px;
    margin-bottom: 10px;
    margin-left: 30px
    }
.refcount {
    color: #060
    }
dt:target, .highlight {
    background-color: #fbe54e
    }
dl.glossary dt {
    font-weight: bold;
    font-size: 1.1em
    }
pre {
    line-height: 120%
    }
pre a {
    /*     color: inherit; */
    text-decoration: underline
    }
.first {
    margin-top: 0 !important
    }
div.document {
    background-color: white;
    text-align: left;
    /* background-image: url(contents.png); */
    background-repeat: repeat-x
    }
/*
div.documentwrapper {
    width: 100%;
}
*/
div.clearer {
    clear: both
    }
div.related h3 {
    display: none
    }
div.related ul {
    /* background-image: url(navigation.png); */
    height: 2em;
    list-style: none;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    margin: 0;
    padding-left: 10px
    }
div.related ul li {
    margin: 0;
    padding: 0;
    height: 2em;
    float: left
    }
div.related ul li.right {
    float: right;
    margin-right: 5px
    }
div.related ul li a {
    margin: 0;
    padding: 0 5px 0 5px;
    line-height: 1.75em;
    color: #ca7900
    }
div.related ul li a:hover {
    color: #3CA8E7
    }
div.body {
    margin: 0;
    padding: 0.5em 20px 20px 3px
    }
div.bodywrapper {
    margin: 0 240px 0 0;
    border-right: 1px solid #ccc
    }
div.body a {
    /* text-decoration: underline */
    }
div.sphinxsidebar {
    margin: 0;
    padding: 0.5em 15px 15px 30px;
    width: 210px;
    float: right;
    text-align: left;
    /*    margin-left: -100%; */
    }
div.sphinxsidebar h4, div.sphinxsidebar h3 {
    margin: 1em 0 0.5em 0;
    font-size: 0.9em;
    padding: 0.1em 0 0.1em 0.5em;
    color: #940000;
    border: 1px solid #940000;
    background-color: #f0ae4a
    }
div.sphinxsidebar ul ul {
    padding-left: 1.5em;
    margin-top: 7px;
    list-style: none;
    padding: 0;
    line-height: 130%
    }
div.sphinxsidebar ul ul {
    list-style: square;
    margin-left: 20px
    }
p {
    margin: 0.8em 0 0.5em 0
    }
p.rubric {
    font-weight: bold
    }
h1 {
    margin: 0;
    padding: 0.7em 0 0.3em 0;
    font-size: 1.5em;
    /*     color: #11557C */
    }
h2 {
    margin: 1.3em 0 0.2em 0;
    font-size: 1.35em;
    padding: 0
    }
h3 {
    margin: 1em 0 -0.3em 0;
    font-size: 1.2em
    }
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    color: #940000;
    text-decoration: none
    }
h1 a.anchor, h2 a.anchor, h3 a.anchor, h4 a.anchor, h5 a.anchor, h6 a.anchor {
    display: none;
    margin: 0 0 0 0.3em;
    padding: 0 0.2em 0 0.2em;
    color: #aaa !important
    }
h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor, h5:hover a.anchor, h6:hover a.anchor {
    display: inline
    }
h1 a.anchor:hover, h2 a.anchor:hover, h3 a.anchor:hover, h4 a.anchor:hover, h5 a.anchor:hover, h6 a.anchor:hover {
    color: #777;
    background-color: #eee
    }
table {
    border-collapse: collapse;
    /*     margin: 0 -0.5em 0 -0.5em */
    }
table td, table th {
    padding: 0.2em 0.5em 0.2em 0.5em
    }
div.footer {
    background-color: #f0ae4a;
    color: #86989B;
    /*     padding: 3px 8px 3px 0; */
    /*     clear: both; */
    /*     font-size: 0.8em; */
    /*     text-align: right; */
    /*     position:absolute; */
    /*     bottom: 0; */
    /*     width: 100% */
    }
div.footer a {
    color: #86989B;
    text-decoration: underline
    }
div.pagination {
    margin-top: 2em;
    padding-top: 0.5em;
    border-top: 1px solid black;
    text-align: center
    }
div.sphinxsidebar ul.toc {
    margin: 1em 0 1em 0;
    padding: 0 0 0 0.5em;
    list-style: none
    }
div.sphinxsidebar ul.toc li {
    margin: 0.5em 0 0.5em 0;
    font-size: 0.9em;
    line-height: 130%
    }
div.sphinxsidebar ul.toc li p {
    margin: 0;
    padding: 0
    }
div.sphinxsidebar ul.toc ul {
    margin: 0.2em 0 0.2em 0;
    padding: 0 0 0 1.8em
    }
div.sphinxsidebar ul.toc ul li {
    padding: 0
    }
div.admonition, div.warning {
    font-size: 0.9em;
    margin: 1em 40px 10px 40px;
    border: 1px solid #86989B;
    background-color: #fffdea
    }
div.admonition p, div.warning p {
    margin: 0.5em 1em 0.5em 1em;
    padding: 0
    }
div.admonition pre, div.warning pre {
    margin: 0.4em 1em 0.4em 1em
    }
div.admonition p.admonition-title, div.warning p.admonition-title {
    margin: 0;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    border-bottom: 1px solid #86989B;
    font-weight: bold;
    background-color: #fe6f20
    }
div.warning {
    border: 1px solid #940000
    }
div.warning p.admonition-title {
    background-color: #CF0000;
    border-bottom-color: #940000
    }
div.admonition ul, div.admonition ol, div.warning ul, div.warning ol {
    margin: 0.1em 0.5em 0.5em 3em;
    padding: 0
    }
div.versioninfo {
    margin: 1em 0 0 0;
    border: 1px solid #ccc;
    background-color: #DDEAF0;
    padding: 8px;
    line-height: 1.3em;
    font-size: 0.9em
    }
a.headerlink {
    color: #c60f0f !important;
    font-size: 1em;
    margin-left: 6px;
    padding: 0 4px 0 4px;
    text-decoration: none !important;
    visibility: hidden
    }
h1:hover > a.headerlink, h2:hover > a.headerlink, h3:hover > a.headerlink, h4:hover > a.headerlink, h5:hover > a.headerlink, h6:hover > a.headerlink, dt:hover > a.headerlink {
    visibility: visible
    }
a.headerlink:hover {
    background-color: #ccc;
    color: white !important
    }
table.indextable td {
    text-align: left;
    vertical-align: top
    }
table.indextable dl, table.indextable dd {
    margin-top: 0;
    margin-bottom: 0
    }
table.indextable tr.pcap {
    height: 10px
    }
table.indextable tr.cap {
    margin-top: 10px;
    background-color: #f2f2f2
    }
img.toggler {
    margin-right: 3px;
    margin-top: 3px;
    cursor: pointer
    }
form.pfform {
    margin: 10px 0 20px 0
    }
table.contentstable {
    width: 90%
    }
table.contentstable p.biglink {
    line-height: 150%
    }
a.biglink {
    font-size: 1.3em
    }
span.linkdescr {
    font-style: italic;
    padding-top: 5px;
    font-size: 90%
    }
ul.search {
    margin: 10px 0 0 20px;
    padding: 0
    }
ul.search li {
    padding: 5px 0 5px 20px;
    background-image: url(file.png);
    background-repeat: no-repeat;
    background-position: 0 7px
    }
ul.search li a {
    font-weight: bold
    }
ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left
    }
ul.keywordmatches li.goodmatch a {
    font-weight: bold
    }
div.sidebar {
    margin-left: 1em;
    margin-top: 1em;
    border: 1px solid orange;
    padding: 1em;
    background-color: #FFFFCC;
    width: 40%;
    float: right;
    margin-bottom: 20px
    }
div.highlight-python {
    display: inline-block
    }
/* Menu */
.horizontal_menu ul {
    list-style-type: none;
    margin: 0pt;
    padding: 0pt;
    z-index: 100
    }
/* All' li principale metto un altezza piu piccola e il fon a bold */
.horizontal_menu .mainli {
    line-height: 13px;
    font-weight: bold;
    -moz-border-radius-topleft: 4px;
    background: url(/static/img/color_tabs_left.gif) no-repeat scroll left top #fff;
    color: #FFF;
    float: left;
    letter-spacing: 1px;
    padding: 0 0 1px 3px;
    text-decoration: none
    }
/* settaggi x tt le voci di menu */
.horizontal_menu ul li {
    display: inline;
    float: left;
    line-height: 21px;
    padding: 0px;
    position: relative;
    margin-right: 4px;
    font-weight: normal
    }
.horizontal_menu ul li a {
    color: #ffffff;
    display: block;
    padding: 1px 8px;
    text-decoration: none
    }
/* Colore dei sottomenu quando ci passo sopra */
.horizontal_menu .mainli ul li a:hover {
    background-color: #f0e84a
    }
.horizontal_menu ul li a.active {
    /* background-color: #e6fc1e; */
    background-color: #f0e84a
    }
/* Settaggi per menu principale, quello sempre visibile */
.horizontal_menu .mainfoldericon {
    background: #f89c22 url(/static/img/color_tabs_left.gif) no-repeat left top;
    -moz-border-radius-topleft: 4px;
    float: left;
    color: #ffffff;
    padding: 0 0 1px 3px;
    text-decoration: none;
    letter-spacing: 1px
    }
.horizontal_menu .mainfoldericon span {
    float: left;
    display: block;
    background: transparent url(/static/img/color_tabs_right.gif) no-repeat right top;
    padding: 4px 9px 2px 6px
    }
/* Colore del menu principale quando ci passo sopra */
.horizontal_menu ul li a:hover {
    background-color: #f8c929
    }
/* Settaggi vari per corretta visualizzazione sottomenu */
.horizontal_menu ul li ul {
    display: block;
    left: 0pt;
    position: absolute;
    top: 1em;
    visibility: hidden;
    float: none
    }
.horizontal_menu ul li ul li {
    display: list-item;
    float: none
    }
.horizontal_menu ul li ul li ul {
    left: 159px;
    top: 0pt
    }
.doc_copyright {
    color: #fff
    }
.slogan {
    padding: 0px 30px 1px 1px
    }
#description {
    margin: 15px 0px 0 0;
    position: absolute;
    right: 0;
    top: 45px;
    width: 250px;
    font-style: italic
    }
#description a {
    color: #333
    }
.news {
/*     text-align: center; */
    margin-bottom: 10px;
    background-color: #eee;
    /*   border-width: 0ps 1px 0px 1px; */
    border-color: #ddd;
    padding: 5px 5px 5px 5px;
    border-color: #333;
    border-top-width: 1px;
    border-bottom-width: 1px
    }
.slide h2 {
    -moz-border-radius-topleft: 4px;
    -moz-border-radius-topright: 4px;
    background: none repeat scroll 0 0 #DAD7D3;
    margin: 0;
    padding: 8px 1px 8px 8px;
    color: #333;
    font-size: 24px;
    line-height: 20px;
    margin: 0 0 8px
    }
h1, h2, h3, h4, h5, h6 {
    font-weight: normal
    }
h4 {
    font-weight: normal;
    font-size: 110%;
    border-color: #333333;
    border-style: solid;
    border-width: 0pt 0pt 0px
    }
.slide {
    min-height: 300px;
    /*   font-family:"Trebuchet MS",Arial,Helvetica,sans-serif; */
    font-weight: normal;
    color: #333
    }
.text {
    width: 400px;
    /*    display: inline; */
    /*    float: left; */
    margin-left: 8px;
    margin-right: 8px;
    position: relative;
    font-size: 15px
    }
.img {
    padding: 8px 5px;
    right: 0;
    text-align: center;
    top: 0;
    width: 350px;
    margin-right: 0;
    display: inline;
    float: right;
    margin-left: 8px
    }
h1 {
    font-size: 150%;
    font-size: 26px;
    padding: 8px 0 2px;
    margin-bottom: 25px
    }
/* .slide {  */
/*   padding-left: 20px */
/*  } */
/* .slide ul { */
/*   list-style: disc outside none; */
/*   list-style: disc;  */
/*   margin: 0 0 0 25px; */
/* } */
/* .slide ul li {  */
/*   display: list-item; */
/* } */
/* .slide                              { padding: 10px 30px; } */
.anythingSlider .slide .text ul {
    list-style: disc;
    margin: 0 0 0 25px
    }
.anythingSlider .slide .text ul li {
    display: list-item
    }
.promo {
    float: left;
    font-size: 13px;
    margin: 12px -20px 20px 0;
    overflow: hidden;
    width: 770px;
    color: #a91819;
}
.box {
    /* color: #a91819; */
    /* color: #FFFFFF; */
    float: left;
    /* height: 118px; */
    margin: 0 19px 0 0;
    padding: 9px 0 0 10px;
    /* background-color: #ddd; */
    -moz-border-radius: 8px;
    background: url("../_static/sfondo-box.png") no-repeat scroll 0 0 transparent; 
}
.promo h1 {
    color: #fff;
}
.promo p {
    color: #333;
}
.promo a {
    color: #fff;
}
.box1 {
    width: 330px;
}
.box2 {
    width: 350px;
}
.box h1 sname {
    color: #a91819;
}
.box sname {
    color: #a91819;
}
.slider-text {
    color: #fff;
}
