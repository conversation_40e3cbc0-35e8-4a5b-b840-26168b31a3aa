import os
import tempfile
import atexit
import shutil

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Table, Column, ForeignKey, types
from sqlalchemy.orm import relation, backref

from sqlkit.db.utils import Descr
from sqlkit.db import proxy

Base = declarative_base()
DB_DIR = tempfile.mkdtemp()
DB_FILE = os.path.join(DB_DIR, 'db-%s.sqlite' % os.environ.get('USERNAME'))
engine = "sqlite:///%s" % DB_FILE
db = proxy.DbProxy(engine=engine, metadata=Base.metadata)


movie_casting = Table(
    'movie_casting', Base.metadata,
    Column('movie_id', types.Integer, ForeignKey('movie.id'), primary_key=True),
    Column('actor_id', types.Integer, ForeignKey('actor.id'), primary_key=True),
    Column('main', types.Boolean, nullable=True),
)

movie_genre = Table(
    'movie_genre', Base.metadata,
    Column('movie_id', types.Integer, Foreign<PERSON>ey('movie.id'), primary_key=True),
    <PERSON>umn('genre_name', types.Integer, ForeignKey('genre.name'), primary_key=True),
)

SCORE_VALUES = (
    (1, 'Nice'),
    (2, 'Beautifull'),
    (3, 'Great'),
)


class Director(Base):
    __tablename__ = 'director'
    id = Column(types.Integer, primary_key=True)
    last_name = Column(types.String(60), nullable=False)
    first_name = Column(types.String(60), nullable=False)
    nation = Column(types.String(6))

    movies = relation('Movie', backref='director', cascade='all, delete-orphan',)

    def __str__(self):
        return u'%s (%s)' % (self.last_name, self.nation)

    def __repr__(self):
        return u'<Director: %s (%s)>' % (self.last_name, self.nation)


class Movie(Base):
    __tablename__ = 'movie'
    id = Column(types.Integer, primary_key=True)
    title = Column(types.String(60), nullable=False)
    image = Column(types.String(250), info={'render': 'image',
                                      'base_dir': './images',
                                      'thumbnail_size': (30, 30)})
    description = Column(types.String(512))
    year = Column(types.Integer())
    date_release = Column(types.Date())
    director_id = Column(types.Integer, ForeignKey('director.id'), nullable=False,
                         info={'attach_instance': 'director'})
    score = Column(types.Integer, info={'render': 'enum',
                                  'values': SCORE_VALUES})

    actors = relation('Actor', backref='movies', secondary=movie_casting)
    genres = relation('Genre', backref='movies', secondary=movie_genre)

    def __str__(self):
        return u'%s' % self.title

    def __repr__(self):
        return u'<Movie: %s>' % self.title


class Genre(Base):
    __tablename__ = 'genre'
    name = Column(types.Unicode(55), primary_key=True)

    def __repr__(self):
        return u'<Genre "%s">' % self.name


class Actor(Base):
    __tablename__ = 'actor'
    id = Column(types.Integer, primary_key=True)
    first_name = Column(types.String(60), nullable=False)
    last_name = Column(types.String(60))
    nation_cod = Column(types.String(4), ForeignKey('nation.cod'))

    nation = relation('Nation', backref='actors')

    def __repr__(self):
        return u'<Actor %s %s>' % (self.first_name, self.last_name)


class Nation(Base):
    __tablename__ = 'nation'
    cod = Column(types.String(4), primary_key=True)
    nation = Column(types.String(20))


class AllTypes(Base):
    __tablename__ = 'all_types'
    id = Column(types.Integer(), primary_key=True)
    varchar10 = Column(types.String(10), nullable=False)
    varchar200 = Column(types.String(200))
    text = Column(types.Text())
    uni = Column(types.Unicode(10, ))
    uni_text = Column(types.UnicodeText(), nullable=False)
    date = Column(types.Date())
    datetime = Column(types.DateTime(timezone=False))
    datetime_tz = Column(types.DateTime(timezone=True))
    interval = Column(types.Interval())
    time = Column(types.Time(timezone=False))
    time_tz = Column(types.Time(timezone=True))
    integer = Column(types.Integer())
    float = Column(types.Float())
    numeric = Column(types.Numeric(8, 2))
    bool = Column(types.Boolean, nullable=False)
    bool_null = Column(types.Boolean, nullable=True)
#    pickle         = Column(PickleType())


def rm_tmpdir():
    shutil.rmtree(DB_DIR)

atexit.register(rm_tmpdir)
