# -*- mode: sh -*-
_sqledit() {
	local cur prev ENGINES

	COMPREPLY=()
	cur=${COMP_WORDS[COMP_CWORD]}
	prev=${COMP_WORDS[COMP_CWORD-1]}
	ENGINES='sqlite:// postgres:// mysql:// mssql:// oracle:// sybase:// maxdb:// firebird://'

	# an option and URL or a nick
	if [ $COMP_CWORD -eq 1 ] || [[ "$cur" == -* ]]   ; then
 	   COMPREPLY=( $( compgen -W '$ENGINES --url -u --nick -n --table -t --mask -m \
                               --sqltable -T --all-tables -A -c --configure -v --version' -- $cur ) 
	       $(  [ -e ~/.sqledit/nicks ] && perl -ne '{if ($_ =~ m/\[(.*)\]/) {print "$1\n"};} ' ~/.sqledit/nicks|
			grep "^$cur"  ))

	# an URL
	elif [ "$prev" == --url ]  || [ "$prev" == -u ] ;  then
 	   COMPREPLY=( $( compgen -W "$ENGINES" -- $cur ))
	fi

	return 0
}
complete -F _sqledit sqledit
