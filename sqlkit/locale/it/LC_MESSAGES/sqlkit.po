# Italian translations for sqlkit.
# Copyright (C) 2009 ORGANIZATION
# This file is distributed under the same license as the sqlkit project.
# <AUTHOR> <EMAIL>, 2009.
# <PERSON><PERSON>tella <<EMAIL>>, 2009, 2010, 2011, 2014.
msgid ""
msgstr ""
"Project-Id-Version: sqlkit 0.8.5-pre\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2009-01-07 19:38+0100\n"
"PO-Revision-Date: 2014-04-08 19:06+0200\n"
"Last-Translator: <PERSON><PERSON>tella <<EMAIL>>\n"
"Language-Team: Thundersystems\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Virtaal 0.7.1\n"
"Generated-By: Babel 0.9.4\n"

#: bin/sqledit.py:87
msgid "Connection setup"
msgstr "Setup della connessione"

#: bin/sqledit.py:90
msgid ""
"\n"
"    You can indicate the database you want to connect to\n"
"    using an URI in the form as:\n"
"    <b>postgres://localhost/dbname</b>\n"
"    <b>sqlite:///dbname</b>\n"
"    or using password and username:\n"
"    <b>mysql://sandro:pass@host/dbname</b>\n"
"    "
msgstr ""
"Puoi indicare il database a cui connetterti\n"
"usando un URI in una delle forme seguenti:\n"
"<b>postgres://localhost/dbname</b>\n"
"<b>sqlite:///dbname</b>\n"
"o usando password e username:\n"
"<b>mysql://sandro:pass@host/dbname</b>"

#: bin/sqledit.py:117
msgid "Run Demo"
msgstr "Avvia Demo"

#: bin/sqledit.py:140
msgid ""
"Info on available backend:\n"
"postgres, mysql..."
msgstr ""
"Informazioni sui backend disponibili:\n"
"postgres, mysql,..."

#: bin/sqledit.py:141
msgid "Sqledit manual page"
msgstr "Pagina di manuale di Sqledit"

#: bin/sqledit.py:146
msgid ""
"Try continuosly to connect.\n"
"        Nice and usefull but may cause temporary blocks\n"
"        if you write an inexistent hostname\n"
"        "
msgstr ""
"Prova a connettersi continuamente:\n"
"utile ma può causare blocchi temporanei\n"
"se scrivi un nome host inesistente"

#: bin/sqledit.py:160
msgid ""
"If you write a wrong hostname\n"
"the application may hang some seconds till the natural network timeout. \n"
"Uncheck the flag on the right ti disable this feature"
msgstr ""
"Se scrivi un nome host errato\n"
"l'applicazione potrebbe bloccarsi \n"
"alcuni secondi fino al timeout di rete\n"
"Smarca la casella a fianco per disabilitare questo comportamento"

#: bin/sqledit.py:187
#, python-format
msgid "Attempting to connect to %s"
msgstr "Sto connettendomi a %s"

#: bin/sqledit.py:190
#, python-format
msgid "Connected to %s"
msgstr "Connesso a %s"

#: bin/sqledit.py:221
msgid "A complete demo of all the features of the sqlkit package"
msgstr ""
"Una demo completa di tutte le caratteristiche\n"
"del pacchetto sqlkit"

#: bin/sqledit.py:222
msgid ""
"The demo was not found, if you know where it is,\n"
"run it manually: python demo.py"
msgstr ""
"La demo non è stata trovata\n"
"se sai dove è provala manualmente:\n"
"python demo.py"

#: sqlkit/exc.py:48
#, python-format
msgid "Field '%s' cannot be NULL"
msgstr "il campo '%s' non può esser NULLO"

#: sqlkit/exc.py:60
#, python-format
msgid "Field '%s' cannot have value %s"
msgstr "il campo '%s' non può avere valore %s"

#: sqlkit/fields.py:979
#, python-format
msgid "value is not Decimal nor string: %s"
msgstr "il valore fornito non è né un Decimal né una stringa: %s"

#: sqlkit/fields.py:1055
#, python-format
msgid "value is not date nor string: %s"
msgstr "il valore fornito non è né una data né una stringa: %s"

#: sqlkit/fields.py:1174 sqlkit/widgets/mask/miniwidgets.py:430
#, python-format
msgid "Wrong date format: %s"
msgstr "Formato data errato: %s"

#: sqlkit/fields.py:1184
msgid "Wrong time format"
msgstr "Formato di tempo errato"

#: sqlkit/fields.py:1336
msgid "Value cannot be NULL"
msgstr "Il valore non può essere NULLO"

#: sqlkit/fields.py:1340
#, python-format
msgid "Value '%s' is not accepted"
msgstr "Il valore '%s' non è permesso"

#: sqlkit/db/proxy.py:219
#, python-format
msgid "Table %s doesn't have a primary key, editing is not possible"
msgstr "La tabella %s non ha una chiave primaria. Non è possibile editarla."

#: sqlkit/layout/dateedit.py:190
msgid "Press Esc to close or double click a date to select it"
msgstr "Premi 'Esc' per chiudere o fai doppio click su una data per selezionarla"

#: sqlkit/layout/fk_entry.py:25
msgid "Find allowed values (Control-Enter/Shift-Enter)"
msgstr "Trova i valori permessi (Control-Invio/Maiusc-Invio)"

#: sqlkit/layout/image_widget.py:142
msgid ""
"Right click on the image area\n"
" to upload an image"
msgstr "Click destro sulla area dell'immagine per caricare una immagine"

#: sqlkit/layout/image_widget.py:273 sqlkit/widgets/table/table.py:1395
msgid "Upload image"
msgstr "Carica Immagine"

#: sqlkit/layout/image_widget.py:273
msgid "Upload or modify an image"
msgstr "Carica o modifica immagine"

#: sqlkit/layout/image_widget.py:274
msgid "Delete image"
msgstr "Elimina immagine"

#: sqlkit/layout/image_widget.py:274
msgid "Delete the image"
msgstr "Elimina l'immagine"

#: sqlkit/layout/image_widget.py:275
msgid "Image viewer"
msgstr "Visualizzatore immagine"

#: sqlkit/layout/image_widget.py:275
msgid "Open separate image viewer"
msgstr "Apri un visualizzatore separato"

#: sqlkit/layout/image_widget.py:276
msgid "Save image as"
msgstr "Salva immagine"

#: sqlkit/layout/image_widget.py:279
msgid "Show/Hide image name"
msgstr "Mostra/Nascondi il nome dell'immagine"

#: sqlkit/layout/image_widget.py:317
msgid "upload image"
msgstr "Carica Immagine"

#: sqlkit/misc/datetools.py:152
msgid "Incomplete date format"
msgstr "Formato di data incompleto: %s"

#: sqlkit/misc/table_browser.py:135
msgid "Database"
msgstr "Database"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:136 sqlkit/misc/words.py:13
#: sqlkit/widgets/common/sqlwidget.py:795
msgid "Modify"
msgstr "Modifica"

#: sqlkit/misc/table_browser.py:137
msgid "Tool"
msgstr "Strumenti"

#. TIP: menu entry
#: sqlkit/misc/table_browser.py:138 sqlkit/misc/words.py:15
#: sqlkit/widgets/common/sqlwidget.py:798
msgid "Help"
msgstr "Aiuto"

#: sqlkit/misc/table_browser.py:141
msgid "Count records"
msgstr "Conta i record"

#: sqlkit/misc/table_browser.py:141
msgid "Count records in all tables"
msgstr "Conta i record in tutte le tabelle"

#: sqlkit/misc/table_browser.py:144
msgid ""
"Configure the fields: \n"
"labels, tooltip, completion, search field"
msgstr ""
"Configura i campi:\n"
"etichette, suggerimenti, completamento, campi di ricerca"

#: sqlkit/misc/table_browser.py:147
msgid "Edit Sqlkit Fields"
msgstr "Edita i campi di sqlkit"

#: sqlkit/misc/table_browser.py:150
msgid "Primary Key"
msgstr "Chiave Prim."

#: sqlkit/misc/table_browser.py:151
msgid "Show/Hide primary key if the key is numeric"
msgstr "Mostra/nascondi la chiave primaria se numerica"

#: sqlkit/misc/table_browser.py:152
msgid "Load data"
msgstr "Carica dati"

#: sqlkit/misc/table_browser.py:153
msgid "Load the data as well"
msgstr "Carica anche i dati (quando apre una tabella)"

#: sqlkit/misc/table_browser.py:154
msgid "Blank"
msgstr "Valori Nulli"

#: sqlkit/misc/table_browser.py:154
msgid "Cast blank into NULL"
msgstr "Trasforma stringhe vuote in NULL"

#: sqlkit/misc/table_browser.py:262
msgid "Mask"
msgstr "Maschera"

#: sqlkit/misc/table_browser.py:266
msgid "Table"
msgstr "Tabella"

#: sqlkit/misc/table_browser.py:272
msgid "Collapse row"
msgstr "Nascondi le informazioni "

#: sqlkit/misc/table_browser.py:276
msgid "Table reflection"
msgstr "Introspezione della tabella"

#: sqlkit/misc/table_browser.py:423
msgid "The name of the table we are customizing"
msgstr "Nome della tabella da personalizzare"

#: sqlkit/misc/table_browser.py:424
#, python-format
msgid ""
"The best representation of a record \n"
"as a combination of fields, e.g.: %(title)s %(year)s"
msgstr ""
"La migliore rappresentazione di un record\n"
"come combinazione di campi, ad esempio: %(title)s %(year)s"

#: sqlkit/misc/table_browser.py:427
msgid "The field that will be searched for when completion is used"
msgstr ""
"Il campo che sarà utilizzato nella ricerca quando viene usato il "
"completamento. Es.: description "

#. TIP: filter page of the filter panel
#: sqlkit/misc/words.py:7
msgid "filter"
msgstr "Filtri"

#. TIP: output page of the filter panel
#: sqlkit/misc/words.py:9 sqlkit/widgets/common/sqlfilter.py:259
#: sqlkit/widgets/common/sqlfilter.py:306
msgid "output"
msgstr "Risultati"

#. TIP: menu entry
#: sqlkit/misc/words.py:11
msgid "File"
msgstr "File"

#. TIP: menu entry
#: sqlkit/misc/words.py:17 sqlkit/widgets/common/sqlwidget.py:796
msgid "Go"
msgstr "Vai"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:19
msgid "opts"
msgstr "Opzioni"

#. TIP: opts in filetr panel
#: sqlkit/misc/words.py:21 sqlkit/widgets/common/sqlwidget.py:797
msgid "Tools"
msgstr "Strumenti"

#. TIP: serach mode:
#: sqlkit/misc/words.py:23
msgid "regexp"
msgstr "espressione regolare"

#. TIP: search mode
#: sqlkit/misc/words.py:25
msgid "start"
msgstr "inizio parola"

#. TIP: count records in sqledit
#: sqlkit/misc/words.py:27
msgid "N.Rows"
msgstr "Numero Records"

#: sqlkit/misc/words.py:29
msgid "Indexes"
msgstr "Indici"

#: sqlkit/misc/words.py:30
msgid "Nullable"
msgstr "Nullo?"

#: sqlkit/misc/words.py:31
msgid "Prim. Keys"
msgstr "Chiave primaria"

#: sqlkit/misc/words.py:32
msgid "Default"
msgstr "Default"

#: sqlkit/misc/words.py:33
msgid "Foreign Key"
msgstr "Foreig Key"

#: sqlkit/misc/words.py:34
msgid "filter_tables"
msgstr "Filtra tabelle"

#. TIP: status bar
#: sqlkit/widgets/common/completion.py:285
#: sqlkit/widgets/common/completion.py:1124
#, python-format
msgid "search mode: %s"
msgstr "modo di ricerca: %s"

#: sqlkit/widgets/common/completion.py:643
msgid "Show all info on this field, db type & Co."
msgstr "Mostra tutte le informazioni su questo campo (tipo del database & Co.)"

#. TIP: menu enty in menu on right click on down arro in fkey completion widget
#: sqlkit/widgets/common/completion.py:649
msgid "Show possible values: regexp  - Ctrl-Enter"
msgstr "Mostra possibili valori con espressione regolare: Ctrl-Invio"

#. TIP: yellow tip to menu entry in down arrow in completion widget
#: sqlkit/widgets/common/completion.py:653
msgid "Show all values in the db that match your string"
msgstr ""
"Mostra tutti valori del database che contengono la tua stringa "
"(espressione regolare)"

#: sqlkit/widgets/common/completion.py:658
msgid "Show possible values, starting - Shift-Enter"
msgstr "Mostra possibili valori con match sulle prima parole Maiusc-Invio"

#: sqlkit/widgets/common/completion.py:661
msgid "Show all values in the database starting with your string"
msgstr "Mostra tutti i valori nel database che cominciano con la tua string"

#: sqlkit/widgets/common/completion.py:667
msgid "Edit the referenced table in 'table' mode"
msgstr "Edita la tabella referenziata in modalità 'tabella'"

#: sqlkit/widgets/common/completion.py:669
msgid "Edit the table from which admitted values are taken in 'table' mode"
msgstr ""
"Edita la tabella nella quale sono definiti i valori ammessi in modalità "
"'tabella'"

#: sqlkit/widgets/common/completion.py:674
msgid "Edit the referenced table in 'mask' mode"
msgstr "Edita la tabella referenziata in modalità maschera"

#: sqlkit/widgets/common/completion.py:676
msgid "Edit the table from which admitted values are taken in 'mask' mode"
msgstr "Edita la tabella dove sono definiti i valori ammessi come maschera"

#: sqlkit/widgets/common/completion.py:783
msgid "Good match"
msgstr "Riferimento trovato"

#: sqlkit/widgets/common/completion.py:792
#: sqlkit/widgets/common/completion.py:798
msgid "No exact match, trying regexp completion"
msgstr "Non c'è un riferimento corretto, cerco un completamento"

#: sqlkit/widgets/common/dialogs.py:26 sqlkit/widgets/common/dialogs.py:94
#: sqlkit/widgets/table/table.py:1703
msgid "All files"
msgstr "Tutti i file"

#: sqlkit/widgets/common/dialogs.py:100
msgid "Images"
msgstr "Immagini"

#: sqlkit/widgets/common/dialogs.py:106
msgid "Documents"
msgstr "Documenti"

#. TIP: possible new name for an uploaded file
#: sqlkit/widgets/common/dialogs.py:123
msgid "Preferred new filename:"
msgstr "Nome file preferito"

#: sqlkit/widgets/common/dialogs.py:124
msgid "If not empty, the file will be uploaded with this new name"
msgstr "Se valorizzato, il file sarà caricato con questo nome"

#: sqlkit/widgets/common/sqlfilter.py:107
#: sqlkit/widgets/common/sqlfilter.py:110
msgid "Match as regexp"
msgstr "Corrispondenza su espressione regolare"

#: sqlkit/widgets/common/sqlfilter.py:108
msgid "Match as LIKE, \"%\" automatically added"
msgstr "Come LIKE, '%' viene aggiunto automaticamente"

#: sqlkit/widgets/common/sqlfilter.py:109
msgid "Match as LIKE case insensitive, \"%\" automatically added"
msgstr "Come LIKE, ignora differenze maiuscole"

#: sqlkit/widgets/common/sqlfilter.py:111
msgid "Match as regexp, case insensitive"
msgstr "Corrispondenza su espressione regolare, ignora differenza maiuscole "

#: sqlkit/widgets/common/sqlfilter.py:112
msgid "Negation of match as regexp"
msgstr "Negazione di espressione regolare"

#: sqlkit/widgets/common/sqlfilter.py:113
msgid "Negation of match case insensitive"
msgstr "Negazione espressione regolare, ignora maiuscole"

#: sqlkit/widgets/common/sqlfilter.py:114
msgid "Equal"
msgstr "Uguale"

#: sqlkit/widgets/common/sqlfilter.py:115
msgid "Not equal"
msgstr "Non uguale"

#: sqlkit/widgets/common/sqlfilter.py:116
msgid "Greater then (after than)"
msgstr "Più grande di (o dopo di)"

#: sqlkit/widgets/common/sqlfilter.py:117
msgid "Greater or equal"
msgstr "Maggiore o uguale"

#: sqlkit/widgets/common/sqlfilter.py:118
msgid "Less than (before then)"
msgstr "Minore di (o prima di)"

#: sqlkit/widgets/common/sqlfilter.py:119
msgid "Less than or equal"
msgstr "Minore o uguale"

#: sqlkit/widgets/common/sqlfilter.py:120
msgid "LIKE: a \"%\" means any char - case sensitive"
msgstr "LIKE: un '%'  significa qualunque carattere"

#: sqlkit/widgets/common/sqlfilter.py:121
msgid "Negation of LIKE"
msgstr "Negazione di LIKE"

#: sqlkit/widgets/common/sqlfilter.py:122
msgid "As LIKE but case insensitive"
msgstr "Come LIKE ma ignora diferenza maiuscole minuscole"

#: sqlkit/widgets/common/sqlfilter.py:123
msgid "Negation of ILIKE"
msgstr "Negazione di ILIKE"

#: sqlkit/widgets/common/sqlfilter.py:124
msgid "The boolean is True"
msgstr "È vero"

#: sqlkit/widgets/common/sqlfilter.py:125
msgid "The boolean is False"
msgstr "È Falso"

#: sqlkit/widgets/common/sqlfilter.py:126
msgid "The boolean is not True"
msgstr "Il valore booleano non è 'vero'"

#: sqlkit/widgets/common/sqlfilter.py:127
msgid "The boolean is not False"
msgstr "Il valore boolenao non è 'False'"

#: sqlkit/widgets/common/sqlfilter.py:128
msgid "The value is not set"
msgstr "Il valore non è impostato"

#: sqlkit/widgets/common/sqlfilter.py:129
msgid "The value is set"
msgstr "Il valore è impostato"

#: sqlkit/widgets/common/sqlfilter.py:130
msgid "ID equality (don't follow foreign table)"
msgstr "Uguaglianza esatta dell'ID (non seguire la tabella referenziata)"

#: sqlkit/widgets/common/sqlfilter.py:221
msgid "Filter Panel"
msgstr "Pannello dei filtri"

#: sqlkit/widgets/common/sqlfilter.py:230
msgid "Add filters for your query"
msgstr "Aggiungi filtri per la tua ricerca"

#: sqlkit/widgets/common/sqlfilter.py:232
msgid "Result page for your query"
msgstr "Risultati della ricerca"

#: sqlkit/widgets/common/sqlfilter.py:269
msgid "Filter actions"
msgstr "Azioni sui filtri"

#: sqlkit/widgets/common/sqlfilter.py:270
msgid "Reload from db"
msgstr "Ricarica dal database"

#: sqlkit/widgets/common/sqlfilter.py:271
msgid "Close the panel"
msgstr "Pannello dei filtri"

#: sqlkit/widgets/common/sqlfilter.py:274
msgid "Go to filter panel"
msgstr "Vai al pannello dei filtri"

#: sqlkit/widgets/common/sqlfilter.py:276
msgid "Go to output panel"
msgstr "Vai alla scheda dei risultati"

#. TIP: status bar message
#: sqlkit/widgets/common/sqlfilter.py:479 sqlkit/widgets/table/table.py:762
#: sqlkit/widgets/table/table.py:1039
#, python-format
msgid "Total N. of records: %s"
msgstr "N. totale di record: %s"

#: sqlkit/widgets/common/sqlfilter.py:801
#, python-format
msgid "value '%s' cannot be used for field '%s'"
msgstr "il valore '%s' non può essere usato per il campo '%s'"

#: sqlkit/widgets/common/sqlfilter.py:868
#, python-format
msgid ""
"value '%s' does not seem a valid date and cannot be transformed into a "
"date"
msgstr "Il valore '%s' non è una data e non può essere trasformato in una data."

#. TIP: appears in the menu in the filter panel to add a second entry of the
#. same field
#: sqlkit/widgets/common/sqlfilter.py:1042
#, python-format
msgid "Add a new filter on this field '%s'"
msgstr "Aggiungi un filtro per il campo '%s'"

#: sqlkit/widgets/common/sqlfilter.py:1048
msgid "Use this filter"
msgstr "Abilita filtro"

#: sqlkit/widgets/common/sqlfilter.py:1092
msgid "Click here to select an operator"
msgstr "Clicca per selezionare un operatore"

#: sqlkit/widgets/common/sqlfilter.py:1164
msgid "incomplete date format"
msgstr "Formato data incompleto"

#: sqlkit/widgets/common/sqlwidget.py:358
#, python-format
msgid ""
"Sorry, problems connecting to remote db. Original error was: \n"
"\n"
"%s"
msgstr ""
"Spiacente, problemi nella connessione al db. \n"
"\n"
"L' errore originale era: %s"

#: sqlkit/widgets/common/sqlwidget.py:625
msgid "Hiding field is only supported for Tables"
msgstr "E' possibile nascondere le colonne solo in modo Tabella"

#: sqlkit/widgets/common/sqlwidget.py:792
msgid "Show all the differences that should be saved to database"
msgstr "Mostra tutte le modifiche che dovrebbero essere salvate nel data"

#: sqlkit/widgets/common/sqlwidget.py:802
msgid "Pending Differences"
msgstr "Modifiche non salvate"

#: sqlkit/widgets/common/sqlwidget.py:806
msgid "Save current record"
msgstr "Salva il record corrente"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Filter panel"
msgstr "Pannello dei filtri"

#: sqlkit/widgets/common/sqlwidget.py:810
msgid "Add filter panel"
msgstr "Aggiungi il pannello dei filtri"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload"
msgstr "Ricarica"

#: sqlkit/widgets/common/sqlwidget.py:811
msgid "Reload from the database"
msgstr "Carica dal database"

#: sqlkit/widgets/common/sqlwidget.py:815
msgid "Go to next record"
msgstr "Vai al prossimo record"

#: sqlkit/widgets/common/sqlwidget.py:816
msgid "Go to previous record"
msgstr "Vai al record precedente"

#: sqlkit/widgets/common/sqlwidget.py:823
msgid "Inspect widgets"
msgstr "Ispeziona la gerarchia degli oggetti GTK (debug)"

#: sqlkit/widgets/common/sqlwidget.py:990
msgid "Saved"
msgstr "Salvato"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1020
#, python-format
msgid ""
"Error while writing to the database. \n"
"Original error was: \n"
"%s"
msgstr ""
"Errore durante la scrittura nel database: \n"
"\n"
"L' errore originale era: %s"

#. TIP: Error while saving into a table w/o permission
#. TIP: reloading data from the database
#: sqlkit/widgets/common/sqlwidget.py:1026
#: sqlkit/widgets/common/sqlwidget.py:1563
#, python-format
msgid ""
"A programming error was received from the backend:\n"
"%s"
msgstr ""
"Un errore di programmazione è stato ricevuto dal backend:\n"
"%s"

#: sqlkit/widgets/common/sqlwidget.py:1035
#, python-format
msgid "%s - Dirty objects: %s - New objects: %s"
msgstr "%s Record non salvati: %s -- Record nuovi: %s"

#. TIP: message in the status bar when a commit error is handled
#: sqlkit/widgets/common/sqlwidget.py:1041
#, python-format
msgid ""
"Error while writing to the database: \n"
"are you 'saving as new' an already known record?\n"
"original error was: \n"
"%s"
msgstr ""
"Errore durante la scrittura nel database: \n"
"stai 'salvando come nuovo' un record già noto (stessa primary key)\n"
"L' errore originale era: %s"

#: sqlkit/widgets/common/sqlwidget.py:1186
msgid "Discarding new obj"
msgstr "Elimino il nuovo oggetto"

#: sqlkit/widgets/common/sqlwidget.py:1223
#, python-format
msgid "Char %s is not accepted in numeric context"
msgstr "Il carattere %s non è utilizzabile in un campo numerico"

#: sqlkit/widgets/common/sqlwidget.py:1281
msgid "record validation"
msgstr "validazione di record"

#: sqlkit/widgets/common/sqlwidget.py:1503
msgid "Unsaved differences"
msgstr "Modifiche non salvate"

#: sqlkit/widgets/common/sqlwidget.py:1741
msgid "Sorry, export is implemented only for table view"
msgstr "Spiacente: è possibile esportare dati solo in modo Tabella"

#: sqlkit/widgets/common/sqlwidget.py:2097
msgid "Property"
msgstr "Proprietà"

#: sqlkit/widgets/common/sqlwidget.py:2098
msgid "Value"
msgstr "Valore"

#: sqlkit/widgets/common/sqlwidget.py:2184
msgid "Save unsaved data?"
msgstr "Salvo i dati non salvati?"

#: sqlkit/widgets/common/sqlwidget.py:2187
msgid "Unsaved data"
msgstr "Dati non salvati"

#: sqlkit/widgets/common/sqlwidget.py:2209
msgid "State"
msgstr "Stato"

#: sqlkit/widgets/common/sqlwidget.py:2210
#: sqlkit/widgets/common/sqlwidget.py:2330
msgid "Field name"
msgstr "Nome del campo"

#: sqlkit/widgets/common/sqlwidget.py:2211
msgid "Original value"
msgstr "Valore originale"

#: sqlkit/widgets/common/sqlwidget.py:2212
msgid "Present value"
msgstr "Valore attuale"

#: sqlkit/widgets/common/sqlwidget.py:2242
msgid "Modified"
msgstr "Modifica"

#: sqlkit/widgets/common/sqlwidget.py:2269 sqlkit/widgets/mask/mask.py:597
msgid "Deleted"
msgstr "Cancellato"

#: sqlkit/widgets/common/sqlwidget.py:2279
msgid "New"
msgstr "Nuovi"

#: sqlkit/widgets/common/sqlwidget.py:2293
msgid "empty string: ''"
msgstr "stringa vuota: ''"

#: sqlkit/widgets/common/sqlwidget.py:2295
msgid "NULL value"
msgstr "Valore NULLO"

#: sqlkit/widgets/common/sqlwidget.py:2301
msgid ""
"Errors are present in the record. \n"
"Correct them now, to continue \n"
"or delete the record"
msgstr ""
"In questo record sono presenti errori. \n"
"Correggili ora per continuare \n"
"o cancella il record"

#: sqlkit/widgets/common/sqlwidget.py:2302
msgid "Validation errors"
msgstr "Errori nella validazione"

#: sqlkit/widgets/common/sqlwidget.py:2331
msgid "Error"
msgstr "Errore"

#: sqlkit/widgets/common/sqlwidget.py:2362
msgid ""
"You can continue or go back editing. \n"
"Read the following warnings to decide"
msgstr ""
"Puoi continuare o modificare il campo. \n"
"Leggi quanto segue per decidere "

#: sqlkit/widgets/common/sqlwidget.py:2363
msgid "Validation Warnings"
msgstr "Ammonimenti di validazione"

#: sqlkit/widgets/mask/mask.py:258
msgid "Add new record"
msgstr "Aggiungi un record nuovo"

#: sqlkit/widgets/mask/mask.py:262
msgid "Discard changes"
msgstr "Annulla le modifiche"

#. TIP Modify menu entry in the mask to reread a single record from database
#: sqlkit/widgets/mask/mask.py:264
msgid "Refresh this record"
msgstr "Rileggo questo record?"

#: sqlkit/widgets/mask/mask.py:265
msgid "Reread this record from db"
msgstr "Rileggo questo record?"

#: sqlkit/widgets/mask/mask.py:268
msgid "Delete this record"
msgstr "Cancello questo record?"

#: sqlkit/widgets/mask/mask.py:342 sqlkit/widgets/table/table.py:908
msgid "Already at new record"
msgstr "Già record nuovo"

#: sqlkit/widgets/mask/mask.py:362
#, python-format
msgid "New record %s"
msgstr "Record nuovo %s"

#. TIP message issued when a refresh is done on a deleted record
#: sqlkit/widgets/mask/mask.py:405
msgid "The record is no longer present in the database"
msgstr "Il record non è più presente nel database."

#: sqlkit/widgets/mask/mask.py:473 sqlkit/widgets/table/table.py:838
msgid "Nothing to save"
msgstr "Niente da salvare"

#: sqlkit/widgets/mask/mask.py:523
#, python-format
msgid "Primary key (%s) didn't change. Refusing to save as new"
msgstr ""
"La chiave primaria (%s) non è stata cambiata. Mi rifiuto di \"salvare "
"come nuovo\""

#: sqlkit/widgets/mask/mask.py:537
msgid "Do you want to copy all data to a new record?"
msgstr "Vuoi copiare tutti i dati in un nuovo record?"

#: sqlkit/widgets/mask/mask.py:585 sqlkit/widgets/table/table.py:982
#, python-format
msgid ""
"Delete this record?\n"
"(%s)"
msgstr ""
"Cancello questo record\n"
"(%s)?"

#: sqlkit/widgets/mask/mask.py:654
msgid "No record present"
msgstr "Nessun record presente"

#: sqlkit/widgets/mask/mask.py:677
msgid "Already last record"
msgstr "Già a un record nuovo"

#: sqlkit/widgets/mask/mask.py:679
msgid "Already first record"
msgstr "Già al primo record"

#: sqlkit/widgets/mask/miniwidgets.py:695
#, python-format
msgid "A file with name '%s' already exists. Overwrite?"
msgstr "Esiste già un record con nome '%s': sovrascrivo?"

#: sqlkit/widgets/mask/miniwidgets.py:696
msgid "Upload name conflict"
msgstr "Conflitto di nome"

#: sqlkit/widgets/mask/miniwidgets.py:888
#, python-format
msgid "'%s' may have an invalid value: try completion on that"
msgstr "'%s' potrebbe avere un valore non valido. Tenta il completamento"

#: sqlkit/widgets/table/columns.py:208
msgid "Editing canceled. Restoring original value"
msgstr "Modifica cancellata. Ripristino il valore originale."

#: sqlkit/widgets/table/columns.py:848
#, python-format
msgid "Add a filter on '%s'"
msgstr "Aggiungi un filtro per '%s'"

#: sqlkit/widgets/table/columns.py:858
msgid ""
"Sort on this column reloading from the database, you can used 's' to sort"
" locally"
msgstr ""
"Ordina su questa colonna ricaricando dal database, puoi usare 's' per "
"ordinare localmente"

#: sqlkit/widgets/table/columns.py:861
msgid "Sort on this column locally (w/o touching the database)"
msgstr "Ordina su questa colonna localmente (senza usare il database)"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:871
msgid "Hide this column"
msgstr "Nascondi questa colonna"

#. TIP: column menu opt
#: sqlkit/widgets/table/columns.py:878
msgid "Create total"
msgstr "Crea il totale"

#. TIP: column menu total
#: sqlkit/widgets/table/columns.py:888
#, python-format
msgid "Subtotal on %s"
msgstr "Subtotale su %s"

#. TIP: column menu opt
#. TIP: modify menu entry
#: sqlkit/widgets/table/columns.py:910 sqlkit/widgets/table/columns.py:1293
#: sqlkit/widgets/table/table.py:287
msgid "Show field"
msgstr "Mostra il campo"

#: sqlkit/widgets/table/columns.py:911 sqlkit/widgets/table/columns.py:1294
#: sqlkit/widgets/table/table.py:288
msgid "Hide field"
msgstr "Nascondi il campo"

#: sqlkit/widgets/table/columns.py:938
msgid "day"
msgstr "giorno"

#: sqlkit/widgets/table/columns.py:938
msgid "week"
msgstr "settimana"

#: sqlkit/widgets/table/columns.py:938
msgid "month"
msgstr "mese"

#: sqlkit/widgets/table/columns.py:939
msgid "quarter"
msgstr "trimestre"

#: sqlkit/widgets/table/columns.py:939
msgid "year"
msgstr "anno"

#: sqlkit/widgets/table/columns.py:942
#, python-format
msgid "Subtotals by %s"
msgstr "Subtotale per %s"

#: sqlkit/widgets/table/columns.py:1267
msgid "Right click on this table to show the column again"
msgstr "Clock destro sulla tabella per mostrare nuovamente la colonna"

#: sqlkit/widgets/table/columns.py:1287 sqlkit/widgets/table/table.py:301
msgid "Export"
msgstr "Esporta"

#: sqlkit/widgets/table/columns.py:1287
msgid "Export these data into csv format"
msgstr "Esporta questi dati in formato csv"

#: sqlkit/widgets/table/columns.py:1292
msgid "Adapt width of columns to data"
msgstr "Adatta l larghezza delle colonne ai dati"

#: sqlkit/widgets/table/columns.py:1321
#, python-format
msgid "View '%s' in a Mask"
msgstr "Mostra '%s' in una maschera"

#: sqlkit/widgets/table/table.py:274
msgid "Duplicate"
msgstr "Duplica"

#: sqlkit/widgets/table/table.py:275
msgid "Create a new row as a duplicate of this one"
msgstr "Crea una nuova riga come duplicato di questa"

#: sqlkit/widgets/table/table.py:277
msgid "New child row"
msgstr "Nuova riga figlia"

#: sqlkit/widgets/table/table.py:278
msgid "Create e new row as child of this one"
msgstr "Crea una riga come figlia di questa"

#: sqlkit/widgets/table/table.py:292
msgid "View this record in a Mask"
msgstr "Mostra questo record in una maschera"

#: sqlkit/widgets/table/table.py:293
msgid "View this ForeignKey in a Mask"
msgstr "Mostra questa ForeigKey in una maschera"

#: sqlkit/widgets/table/table.py:294
msgid "Upload Image"
msgstr "Carica Immagine"

#: sqlkit/widgets/table/table.py:295
msgid "Upload File"
msgstr "Carica un file"

#: sqlkit/widgets/table/table.py:296
msgid "Delete File/Image"
msgstr "Elimina immagine/file"

#: sqlkit/widgets/table/table.py:297
msgid "Show File/Image"
msgstr "Mostra il file/immagine"

#: sqlkit/widgets/table/table.py:298
msgid "Save File/Image"
msgstr "Salva immagine/file"

#: sqlkit/widgets/table/table.py:298
msgid "Save file locally"
msgstr "Salva il file localmente"

#: sqlkit/widgets/table/table.py:528 sqlkit/widgets/table/table.py:542
#: sqlkit/widgets/table/table.py:554
msgid "Insertion of new records is disabled. Sorry"
msgstr "L'inserimento di nuovi record è disabilitato"

#: sqlkit/widgets/table/table.py:558
msgid "Update of records is disabled. Sorry"
msgstr "Spiacente, l'aggiornamento di record è disabilitato. "

#: sqlkit/widgets/table/table.py:570
msgid "Deletion of records is disabled. Sorry"
msgstr "La cancellazione di record è disabilitata."

#: sqlkit/widgets/table/table.py:583
msgid "browsing of new records is disabled. Sorry"
msgstr "Non è permesso sfogliare i dati."

#: sqlkit/widgets/table/table.py:731
msgid "no current obj: maybe no record has yet been edited"
msgstr "non esiste un oggetto in uso: forse nessun record è stato modificato"

#. TIP: when saving m2m, we delay till leader record will be saved
#: sqlkit/widgets/table/table.py:869
msgid "delaying saving when main record will be saved"
msgstr "Ritardo il salvataggio quando verrà salvato il record principale"

#: sqlkit/widgets/table/table.py:879
msgid "Record has NOT been saved"
msgstr "Il record NON è stato salvato"

#: sqlkit/widgets/table/table.py:978
msgid "No record selected"
msgstr "Nessun record selezionato"

#: sqlkit/widgets/table/table.py:1137
msgid "Value is not valid, trying completion"
msgstr "Il valore non è valido: provo il completamento"

#. TIP: check in input field length %s %s -> field_name length
#: sqlkit/widgets/table/table.py:1170
#, python-format
msgid "Input exceeded max %s length (%s)"
msgstr "L'input eccede la lunghezza massima per il campo %s (%s)"

#: sqlkit/widgets/table/table.py:1284
msgid "Unsaved data prevent opening a Mask to show the (unsaved) record"
msgstr "Dati non salvati bloccano l'apertura della maschera per mostrare il record"

#: sqlkit/widgets/table/table.py:1407
#, python-format
msgid ""
"A file with name '%s' already exists.\n"
"Overwrite?"
msgstr "Esiste già un record con nome '%s': sovrascrivo?"

#: sqlkit/widgets/table/table.py:1408
msgid "Upload name duplication"
msgstr "Conflitto di nome"

#: sqlkit/widgets/table/table.py:1629
msgid "Export in csv file"
msgstr "Esporta in un file .csv"

#: sqlkit/widgets/table/table.py:1702
msgid "Csv files"
msgstr "file csv"

#: sqlkit/widgets/table/tablewidgets.py:34
#, python-format
msgid "No current obj for field '%s' in %s"
msgstr "Nessun oggetto corrente per il campo '%s' in %s"
