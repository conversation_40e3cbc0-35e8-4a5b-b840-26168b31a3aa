# -*- mode: python -*-

>>> import os
>>> import gtk

#>>> os.environ['LANG'] = 'en_US.utf8'
>>> os.environ['LANG'] = 'en_US.utf8'

>>> import sys
>>> sys.path.insert(0, '../../demo/sql/model')
>>> sys.path.insert(0, '../../')

>>> from movies import Movie, Director, AllTypes, db
>>> from sqlkit.widgets import SqlMask, SqlTable

## entry with dimenstions: uses entry + alignement
>>> lay = "title"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> sm.lay_obj.lay
'L=title ae=title:30-'
>>> sm.laygen.fields_in_layout
{'title': 'e=title'}

## TEXT: widget is a text TextWidget, default w/h (250.100) taken from layoutgenerator
>>> lay = "text"
>>> sm = SqlMask(AllTypes, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=text TXS=text:250.100
>>> sm.laygen.fields_in_layout
{'text': 'TX=text'}

## TEXT: casting the text widget to entry
>>> lay = "e=text"
>>> sm = SqlMask(AllTypes, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=text e=text
>>> sm.laygen.fields_in_layout
{'text': 'e=text'}

## TEXT: casting the entry widget to text
>>> lay = "TXS=description"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> sm.lay_obj.lay
'L=description TXS=description:250.100'
>>> sm.laygen.fields_in_layout
{'description': 'TX=description'}

## TEXT  no label
>>> lay = "TXS==description"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> sm.lay_obj.lay
'TXS=description'
>>> sm.laygen.fields_in_layout
{'description': 'TX=description'}

>>> lay = "date_release"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=date_release d=date_release
>>> sm.laygen.fields_in_layout
{'date_release': 'd=date_release'}

>>> lay = "year"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=year ae=year:8
>>> sm.laygen.fields_in_layout
{'year': 'e=year'}

>>> lay = "time"
>>> sm = SqlMask(AllTypes, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=time ae=time
>>> sm.laygen.fields_in_layout
{'time': 'e=time'}

>>> lay = "director_id"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=director_id fk=director_id
>>> sm.laygen.fields_in_layout
{'director_id': 'fk=director_id'}

>>> lay = "{>>.open title}"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
{>>.open L=title ae=title:30-}
>>> sm.laygen.fields_in_layout
{'title': 'e=title'}

>>> lay = "{>.closed title}"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
{>.closed L=title ae=title:30-}

>>> lay = "m2m=genres"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
m2m=genres
>>> sm.laygen.loaders
{'genres': {}}
>>> sm.laygen.fields_in_layout
{'genres': 'm2m=genres'}

>>> lay = "m2m=genres::name"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
m2m=genres::name
>>> sm.laygen.loaders
{'genres': {'field_list': 'name'}}
>>> sm.laygen.fields_in_layout
{'genres': 'm2m=genres'}

>>> lay = "m2m=genres:2"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
m2m=genres:2
>>> sm.laygen.loaders
{'genres': {'rows': 2}}
>>> sm.laygen.fields_in_layout
{'genres': 'm2m=genres'}

>>> lay = "m2m=genres:2:"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
m2m=genres:2:
>>> sm.laygen.loaders
{'genres': {'rows': 2}}
>>> sm.laygen.fields_in_layout
{'genres': 'm2m=genres'}

# >>> lay = "m2m=genres.film:20"
# >>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
# >>> print sm.lay_obj.lay.strip()
# m2m=genres.film:20
# >>> sm.laygen.loaders
# {'genres': {'rows': 20,}}

>>> lay = "b=gtk-save-as"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
b=gtk-save-as
>>> sm.laygen.fields_in_layout
{}

>>> lay = """
...      title       year:5< 
...      director_id - - 
...      m2m=genres  - m2m=actors -
...      """
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
L=title ae=title:30-       L=year ae=year:5< 
     L=director_id fk=director_id - - 
     m2m=genres  - m2m=actors -

>>> lay = """
...      {|.uno
...         {N  { %title
...               title   year:5<
...             }
...             {  %file
...               m2m=genres m2m=actors -
...             }
...          }
...      }
...  """
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> print sm.lay_obj.lay.strip()
{|.uno
        {N  { %title
              L=title ae=title:30-   L=year ae=year:5<
            }
            {  %file
              m2m=genres m2m=actors -
            }
         }
     }
            
# :30 is wrong for dates and numbers
>>> sm = SqlMask(Movie, dbproxy=db, naked=True, show=True)

# sm.current is added with gobject.idle_add...
>>> while gtk.events_pending():  
...   a=gtk.main_iteration()    

>>> sm.current
<Movie: None>

>>> sm.get_value('title')

>>> sm.widgets['fk=director_id'].set_text('f')

>>> sm.completions.director_id.show_possible_completion(sm.widgets['fk=director_id'], 'start', pop=False) 
Faenza
Fellini
>>> sm.completions.director_id.show_possible_completion(sm.widgets['fk=director_id'], 'regexp', pop=False) 
Faenza
Fellini
Truffaut

# check for the need of the generation of an object to satisfy delete-orphan in backref
# namely: relation director has a backref 'movies' that has cascade.delete_orphan set
# that would prevent setting director_id if the references object is not present in the session
>>> for p in sm.gui_fields.director_id._props_for_delete_orphan: str(p)
'Movie.director'

>>> sm = SqlMask(AllTypes, dbproxy=db, naked=True, show=False)
>>> n = sm.reload()
>>> t = SqlTable(AllTypes, dbproxy=db, naked=True, show=False)

