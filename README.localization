Localization
============


You can read about sqlkit and localization in the docs.

If you feel like contributing, I'd really appreciate you send me a
translation file that you can obtain following the following steps.


  1. copy locale/sqlkit.pot in the folder (to be created) for your language
     following the standard structure. Eg. for italy:

     locale/it/LC_MESSAGES/sqlkit.po

  2. edit it with your preferred tool (emacs, virtaal, poEdit)
     and send me (<EMAIL>) the translation

  3. compile the .po file into a .mo file with:

     python setup.py compile_catalog

it you are updating you translation, substitute step 1. with:

  1b. python setup.py update_catalog

   


