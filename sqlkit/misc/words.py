"""
A collection of words I want translated
This is just a fake module. Too lazy to learn babel extractors...
"""

# TIP: filter page of the filter panel
_('filter')
# TIP: output page of the filter panel
_('output')
# TIP: menu entry
_('File')
# TIP: menu entry
_('Modify')
# TIP: menu entry
_('Help')
# TIP: menu entry
_('Go')
# TIP: opts in filetr panel
_('opts')
# TIP: opts in filetr panel
_('Tools')
# TIP: serach mode:
_('regexp')
# TIP: search mode
_('start')
# TIP: count records in sqledit
_('N.Rows')
# index column in sqledit
_('Indexes')
_('Nullable')
_('Prim. Keys')
_('Default')
_('Foreign Key')
_('filter_tables')
# _('')
# _('')
# _('')
