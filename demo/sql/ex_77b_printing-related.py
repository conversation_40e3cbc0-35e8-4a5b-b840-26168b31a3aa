"""printing/related appy

You can also loop on related rows


"""
from sqlkit.misc import printing
printing.set_printing_style('appy')


def prepare_context(printtool, context, template_name, sqlwidget):
    """
    substitue director's films as table objects
    """
    context['Table1'] = (sqlwidget.related.movies.records,)


lay = """
  first_name last_name
  nation
  o2m=movies - - -

"""

t = SqlMask(model.Director, layout=lay, dbproxy=db)
t.printing.add_menu_entry('Print to pdf -appy', 'director-movies-appy.odt', mode='pdf')
t.printing.add_menu_entry('Print to odt - appy', 'director-movies-appy.odt', mode='odt')
t.printing.connect('context-ready', prepare_context)
t.reload()
## restore default for other examples
printing.set_printing_style('ooo')
