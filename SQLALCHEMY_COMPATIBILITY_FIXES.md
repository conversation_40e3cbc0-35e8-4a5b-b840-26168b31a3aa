# SQLAlchemy Compatibility Fixes for SQLKit

## Problema Risolto

SQLKit utilizzava la sintassi `autoload=True` che è stata **deprecata in SQLAlchemy 1.4** e **rimossa completamente in SQLAlchemy 2.0+**. Questo causava errori di compatibilità quando si tentava di utilizzare SQLKit con versioni moderne di SQLAlchemy.

## Errore Originale

```python
# ❌ Sintassi deprecata/rimossa
Table(name, metadata, autoload=True)
```

**Errore tipico:**
```
TypeError: 'autoload' is an invalid keyword argument for Table
```

## Soluzione Implementata

### 1. Funzione Helper di Compatibilità

**File:** `sqlkit/db/utils.py`

Aggiunta della funzione `_autoload_table()` che gestisce automaticamente la compatibilità tra versioni:

```python
def _autoload_table(table_name, metadata, engine=None):
    """
    Helper function to handle autoload compatibility across SQLAlchemy versions.
    
    :param table_name: Name of the table to autoload
    :param metadata: SQLAlchemy metadata object
    :param engine: SQLAlchemy engine (optional, will use metadata.bind if not provided)
    :return: SQLAlchemy Table object
    """
    if engine is None:
        engine = metadata.bind
    
    if parse_version(sqlalchemy.__version__) >= parse_version('1.4.0'):
        # SQLAlchemy 1.4+ uses autoload_with parameter
        return Table(table_name, metadata, autoload_with=engine)
    else:
        # SQLAlchemy < 1.4 uses autoload=True
        return Table(table_name, metadata, autoload=True)
```

### 2. File Modificati

#### `sqlkit/db/proxy.py`
- **Metodo:** `get_table()` (linea ~101)
- **Metodo:** `join2tables()` (linee ~169, ~176)

```python
# ❌ Prima
return Table(name, self.metadata, autoload=True)

# ✅ Dopo  
return _autoload_table(name, self.metadata)
```

#### `sqlkit/db/utils.py`
- **Metodo:** `TableDescr.get_table()` (linea ~309)

```python
# ❌ Prima
return Table(self.table_name, self.metadata, autoload=True)

# ✅ Dopo
return _autoload_table(self.table_name, self.metadata)
```

#### `sqlkit/misc/table_browser.py`
- **Metodo:** `count_records()` (linea ~204)
- **Metodo:** `get_table_info_in_model()` (linea ~346)

```python
# ❌ Prima
reflected_table = sa.Table(table_name, meta, autoload=True)

# ✅ Dopo
reflected_table = _autoload_table(table_name, meta)
```

#### `sqlkit/widgets/common/sqlfilter.py`
- **Metodo:** `FilterPanel.create_mapper()` (linea ~614)

```python
# ❌ Prima
T[tbl] = sqlalchemy.Table(tbl, self.master.metadata, autoload=True)

# ✅ Dopo
T[tbl] = _autoload_table(tbl, self.master.metadata)
```

#### `demo/sql/model/movies_autoload.py`
- **Linee:** 17, 18, 21, 55

```python
# ❌ Prima
movie_casting = Table('movie_casting', Base.metadata, autoload=True)
movie_genre = Table('movie_genre', Base.metadata, autoload=True)

class Director(Base):
    __table__ = Table('director', Base.metadata, autoload=True)

# ✅ Dopo
movie_casting = _autoload_table('movie_casting', Base.metadata)
movie_genre = _autoload_table('movie_genre', Base.metadata)

class Director(Base):
    __table__ = _autoload_table('director', Base.metadata)
```

## Compatibilità

Le modifiche garantiscono compatibilità con:

- **SQLAlchemy < 1.4**: Usa `autoload=True` (sintassi originale)
- **SQLAlchemy 1.4+**: Usa `autoload_with=engine` (nuova sintassi)
- **SQLAlchemy 2.0+**: Usa `autoload_with=engine` (sintassi richiesta)

## Test

Le modifiche sono state testate per:
- ✅ Correttezza sintattica
- ✅ Logica di versioning
- ✅ Compatibilità retroattiva

## Benefici

1. **Compatibilità Futura**: SQLKit ora funziona con SQLAlchemy 2.0+
2. **Compatibilità Retroattiva**: Continua a funzionare con versioni precedenti
3. **Manutenzione Semplificata**: Un solo punto di controllo per la compatibilità
4. **Transizione Graduale**: Non richiede modifiche immediate al codice utente

## Note per gli Sviluppatori

- La funzione `_autoload_table()` è interna e non dovrebbe essere utilizzata direttamente nel codice utente
- Tutte le modifiche sono backward-compatible
- Non sono richieste modifiche al codice esistente che utilizza SQLKit
