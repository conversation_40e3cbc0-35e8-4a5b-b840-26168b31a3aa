                      ANNOUNCE: sqlkit 0.9.5.1

                          Feb, 26 - 2012


I'm happy to announce release 0.9.5.1 of sqlkit package for Python.

  http://sqlkit.argolinux.org/

This release
------------

Some minor improvements in filters: a new filter operator that adds Empty or
NULL searches (the OR conjunctin was not possible via GUI) and a shortcut to
the process of selecting a filter, writing in it and reloading:
Control-Alt-f fron the mask/table *field* entry (as opposed to "from the
filter's field entry). A fix in the image field whose deletion was not
working. Now works whith libreoffice as well as openoffice as template engine.

The python package
------------------
SQLkit PyGtk package provides Mask and Table widgets to edit database
data. It's meant as a base for database desktop applications.

The application
---------------
It also provides 'sqledit' a PyGTK application based on sqlkit that can be
used from command line to browse and edit data. 

The package has 2 very rich demo suites for sql widgets (the main one in
sqlkit/demo/sql/demo.py) and for layout creation
  
Translations
------------

If you like sqlkit and want to help translating, you may find the project on:
  
  https://launchpad.net/sqlkit

Main features of sqlkit:
------------------------

  * editor of databases in 2 modes: table & mask
  * based on sqlalchemy: can cope with many different databases
  * very powerful filtering capabilities:
        - each field can be used to filter records
	- filter may span relationship
        - date filtering possible also on relative basis (good for saved 
          queries)
  * completion on all text field and foreign keys
  * very easy way to draw a layout for mask views
  * completely effortless editing of relationships
  * very easy way to set defaults
  * possibility to display totals of numeric fields
  * any possible sql constraint can be attached to a Mask or a 
    Table. It can be expressed as a normal sqlalchemy query or with 
    django-like syntax
  * sqledit: the application to edit db
  

Sqlkit is based on:
-------------------
  * python (>= 2.5)
  * PyGtk	
  * Sqlalchemy (>= 0.5.4)
  * python-dateutil
  * babel (localization)
  * you db driver of choice

Download & more:
---------------

  * Download:     http://sqlkit.argolinux.org/misc/download.html
                  easy_install sqlkit
  * Source:       hg clone http://hg.argolinux.org/py/sqlkit
  * Google Group: http://groups.google.it/group/sqlkit/
  * Translation:  https://launchpad.net/sqlkit
  * Tutorial:     http://sqlkit.argolinux.org/misc/tutorials.html
  * Changelog:    http://sqlkit.argolinux.org/download/Changelog
  * License:      GNU GPLv3
  
