# -*- mode: python -*-

>>> from sqlkit.misc import datetools as dt
>>> from datetime import *
>>> dt.TEST = True

>>> dt.TODAY = date(2007, 5, 15)
>>> dt.string2date('d')
datetime.date(2007, 5, 15)

>>> dt.string2date('m')
datetime.date(2007, 5, 1)
>>> dt.string2date('M')
datetime.date(2007, 5, 31)


>>> dt.TODAY = date(2007, 6, 2)

## day
>>> dt.string2date('d')
datetime.date(2007, 6, 2)
>>> dt.string2date('-1d')
datetime.date(2007, 6, 1)
>>> dt.string2date('i')
datetime.date(2007, 6, 1)
>>> dt.string2date('d-5')
datetime.date(2007, 5, 28)
>>> dt.string2date('d+9')
datetime.date(2007, 6, 11)

## week (european stile...)
>>> dt.string2date('w')
datetime.date(2007, 5, 28)
>>> dt.string2date('W')
datetime.date(2007, 6, 3)

>>> dt.string2date('w-1')
datetime.date(2007, 5, 21)
>>> dt.string2date('W-1')
datetime.date(2007, 5, 27)

>>> dt.string2date('w+1')
datetime.date(2007, 6, 4)
>>> dt.string2date('W+1')
datetime.date(2007, 6, 10)


## month
>>> dt.string2date('m')
datetime.date(2007, 6, 1)
>>> dt.string2date('M')
datetime.date(2007, 6, 30)

>>> dt.string2date('m-1')
datetime.date(2007, 5, 1)
>>> dt.string2date('M-1')
datetime.date(2007, 5, 31)
>>> dt.string2date('M-1m')
datetime.date(2007, 5, 31)
>>> dt.string2date('M-2d-1m')
datetime.date(2007, 5, 28)
>>> dt.string2date('M-1m-2d')
datetime.date(2007, 5, 29)

>>> dt.string2date('m+1')
datetime.date(2007, 7, 1)
>>> dt.string2date('M+1')
datetime.date(2007, 7, 31)


## year
>>> dt.string2date('y')
datetime.date(2007, 1, 1)
>>> dt.string2date('Y')
datetime.date(2007, 12, 31)

>>> dt.string2date('y-1')
datetime.date(2006, 1, 1)
>>> dt.string2date('Y-1')
datetime.date(2006, 12, 31)

>>> dt.string2date('y-1')
datetime.date(2006, 1, 1)
>>> dt.string2date('Y-1')
datetime.date(2006, 12, 31)

>>> dt.string2date('y+1')
datetime.date(2008, 1, 1)
>>> dt.string2date('Y+1')
datetime.date(2008, 12, 31)

# mixing. 
>>> dt.string2date('y +2m')
datetime.date(2007, 3, 1)
>>> dt.string2date('y+2m')
datetime.date(2007, 3, 1)
>>> dt.string2date('m+15d')
datetime.date(2007, 6, 16)

# shift to the next working day
>>> dt.string2date('m+15d {12345}')
datetime.date(2007, 6, 18)

# 
>>> dt.string2date('Y -2m')
datetime.date(2007, 10, 31)

>>> dt.string2date('Y -1m')
datetime.date(2007, 11, 30)

>>> dt.string2date('Y -m')
datetime.date(2007, 11, 30)

# periods
>>> dt.string2dates('y > Y')
(datetime.date(2007, 1, 1), datetime.date(2007, 12, 31))

# 
>>> dt.string2dates('y+3m > Y-3m')
(datetime.date(2007, 4, 1), datetime.date(2007, 9, 30))

>>> dt.string2dates('@m')
(datetime.date(2007, 6, 1), datetime.date(2007, 6, 30))

>>> dt.string2dates('@2m')
(datetime.date(2007, 6, 1), datetime.date(2007, 7, 31))

>>> dt.string2dates('@2m-1')
(datetime.date(2007, 5, 1), datetime.date(2007, 6, 30))

>>> dt.string2dates('@w')
(datetime.date(2007, 5, 28), datetime.date(2007, 6, 3))

>>> dt.string2dates('@w+2')
(datetime.date(2007, 6, 11), datetime.date(2007, 6, 17))

>>> dt.string2dates('@m-1')
(datetime.date(2007, 5, 1), datetime.date(2007, 5, 31))

>>> dt.string2dates('@y')
(datetime.date(2007, 1, 1), datetime.date(2007, 12, 31))

>>> dt.string2dates('@y-1')
(datetime.date(2006, 1, 1), datetime.date(2006, 12, 31))

>>> dt.string2dates('@y +1')
(datetime.date(2008, 1, 1), datetime.date(2008, 12, 31))

>>> dt.string2dates('@d')
(datetime.date(2007, 6, 2), datetime.date(2007, 6, 3))

>>> dt.string2dates('@d+1')
(datetime.date(2007, 6, 3), datetime.date(2007, 6, 4))

>>> dt.string2dates('@2d-1')
(datetime.date(2007, 6, 1), datetime.date(2007, 6, 3))

# Not implemented
#>>> dt.string2dates('@m')

