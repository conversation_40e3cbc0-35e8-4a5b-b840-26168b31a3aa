#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test isolato delle funzioni di compatibilità PostgreSQL 12+
"""

def test_postgresql_functions():
    """Test delle funzioni di compatibilità PostgreSQL senza dipendenze SQLKit"""
    
    # Definizione delle funzioni da testare (copiate dal codice)
    def _get_table_names_pg_compatible(engine):
        """
        Helper function to get table names with PostgreSQL 12+ compatibility.
        """
        try:
            # Try the standard SQLAlchemy method first
            return engine.table_names()
        except Exception:
            # Fallback for PostgreSQL 12+ compatibility issues
            if hasattr(engine, 'dialect') and 'postgres' in engine.dialect.name:
                try:
                    # Use direct SQL query for PostgreSQL
                    with engine.connect() as conn:
                        result = conn.execute("""
                            SELECT tablename 
                            FROM pg_tables 
                            WHERE schemaname = 'public'
                            ORDER BY tablename
                        """)
                        return [row[0] for row in result]
                except Exception:
                    # Last resort: try with current_schema()
                    try:
                        with engine.connect() as conn:
                            result = conn.execute("""
                                SELECT tablename 
                                FROM pg_tables 
                                WHERE schemaname = current_schema()
                                ORDER BY tablename
                            """)
                            return [row[0] for row in result]
                    except Exception:
                        # If all else fails, return empty list
                        return []
            else:
                # For non-PostgreSQL databases, return empty list on error
                return []

    def _has_table_pg_compatible(engine, table_name, schema=None):
        """
        Helper function to check table existence with PostgreSQL 12+ compatibility.
        """
        try:
            # Try the standard SQLAlchemy method first
            return engine.has_table(table_name, schema=schema)
        except Exception:
            # Fallback for PostgreSQL 12+ compatibility issues
            if hasattr(engine, 'dialect') and 'postgres' in engine.dialect.name:
                try:
                    # Use direct SQL query for PostgreSQL
                    schema_condition = schema or 'public'
                    with engine.connect() as conn:
                        result = conn.execute("""
                            SELECT COUNT(*) 
                            FROM pg_tables 
                            WHERE tablename = %s AND schemaname = %s
                        """, (table_name, schema_condition))
                        return result.scalar() > 0
                except Exception:
                    # Last resort: try with current_schema()
                    try:
                        with engine.connect() as conn:
                            result = conn.execute("""
                                SELECT COUNT(*) 
                                FROM pg_tables 
                                WHERE tablename = %s AND schemaname = current_schema()
                            """, (table_name,))
                            return result.scalar() > 0
                    except Exception:
                        # If all else fails, assume table doesn't exist
                        return False
            else:
                # For non-PostgreSQL databases, assume table doesn't exist on error
                return False

    # Mock classes per il test
    class MockPostgreSQLEngine:
        def __init__(self, should_fail=False):
            self.should_fail = should_fail
            self.dialect = MockDialect('postgresql')
            
        def table_names(self):
            if self.should_fail:
                raise Exception("PostgreSQL 12+ compatibility issue")
            return ['test_table1', 'test_table2']
            
        def has_table(self, table_name, schema=None):
            if self.should_fail:
                raise Exception("PostgreSQL 12+ compatibility issue")
            return table_name in ['test_table1', 'test_table2']
            
        def connect(self):
            return MockConnection(self.should_fail)
    
    class MockSQLiteEngine:
        def __init__(self):
            self.dialect = MockDialect('sqlite')
            
        def table_names(self):
            raise Exception("Some error")
            
        def has_table(self, table_name, schema=None):
            raise Exception("Some error")
    
    class MockDialect:
        def __init__(self, name):
            self.name = name
    
    class MockConnection:
        def __init__(self, should_fail=False):
            self.should_fail = should_fail
            
        def execute(self, query, params=None):
            if self.should_fail:
                raise Exception("Connection failed")
            return MockResult()
            
        def __enter__(self):
            return self
            
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
    
    class MockResult:
        def __init__(self):
            self.data = [('test_table1',), ('test_table2',)]
            
        def __iter__(self):
            return iter(self.data)
            
        def scalar(self):
            return 1  # Simula che la tabella esiste

    print("Testing PostgreSQL 12+ compatibility functions...")
    
    success = True
    
    # Test 1: Engine PostgreSQL che funziona normalmente
    print("  Test 1: Normal PostgreSQL engine...")
    try:
        engine_ok = MockPostgreSQLEngine(should_fail=False)
        
        tables = _get_table_names_pg_compatible(engine_ok)
        print(f"    ✓ table_names() returned: {tables}")
        assert tables == ['test_table1', 'test_table2']
        
        has_table = _has_table_pg_compatible(engine_ok, 'test_table1')
        print(f"    ✓ has_table('test_table1') returned: {has_table}")
        assert has_table == True
        
    except Exception as e:
        print(f"    ✗ Test 1 failed: {e}")
        success = False
    
    # Test 2: Engine PostgreSQL che fallisce (simula PostgreSQL 12+ issue)
    print("  Test 2: PostgreSQL engine with compatibility issues...")
    try:
        engine_fail = MockPostgreSQLEngine(should_fail=True)
        
        tables_fallback = _get_table_names_pg_compatible(engine_fail)
        print(f"    ✓ table_names() fallback returned: {tables_fallback}")
        # Dovrebbe restituire lista vuota perché il mock fallisce anche nel fallback
        assert isinstance(tables_fallback, list)
        
        has_table_fallback = _has_table_pg_compatible(engine_fail, 'test_table1')
        print(f"    ✓ has_table() fallback returned: {has_table_fallback}")
        # Dovrebbe restituire False perché il mock fallisce anche nel fallback
        assert has_table_fallback == False
        
    except Exception as e:
        print(f"    ✗ Test 2 failed: {e}")
        success = False
    
    # Test 3: Engine non-PostgreSQL
    print("  Test 3: Non-PostgreSQL engine...")
    try:
        engine_sqlite = MockSQLiteEngine()
        
        tables_sqlite = _get_table_names_pg_compatible(engine_sqlite)
        print(f"    ✓ Non-PostgreSQL table_names() returned: {tables_sqlite}")
        assert tables_sqlite == []
        
        has_table_sqlite = _has_table_pg_compatible(engine_sqlite, 'test_table')
        print(f"    ✓ Non-PostgreSQL has_table() returned: {has_table_sqlite}")
        assert has_table_sqlite == False
        
    except Exception as e:
        print(f"    ✗ Test 3 failed: {e}")
        success = False
    
    return success

if __name__ == '__main__':
    print("=" * 60)
    print("SQLKit PostgreSQL 12+ Compatibility Functions Test")
    print("=" * 60)
    
    success = test_postgresql_functions()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All PostgreSQL compatibility function tests passed!")
        print("\nThe compatibility functions should resolve:")
        print("- table_names() issues with PostgreSQL 12+")
        print("- has_table() issues with PostgreSQL 12+")
        print("- Graceful fallback for non-PostgreSQL databases")
    else:
        print("✗ Some tests failed.")
    
    exit(0 if success else 1)
