# -*- mode: python -*-

>>> import sys
>>> sys.path.insert(0, '../../demo/sql/model')
>>> sys.path.insert(0, '../../')
>>> from movies import <PERSON>, Director, AllTypes, db
>>> from sqlkit.db.minspect import InspectMapper
>>> import decimal
>>> import datetime

>>> mapper = AllTypes.__mapper__
>>> ip = InspectMapper(mapper)

>>> def check(field_name):
...    return ip.get_py_type(type(mapper.c[field_name].type), field_name) 

>>> check('id')
<type 'int'>
>>> check('varchar10')
<type 'str'>
>>> check('varchar200')
<type 'str'>
>>> check('text')
<type 'str'>
>>> check('uni')
<type 'str'>
>>> check('uni_text')
<type 'str'>
>>> check('date')
<type 'datetime.date'>
>>> check('datetime')
<type 'datetime.datetime'>
>>> check('interval')
<type 'datetime.timedelta'>
>>> check('time')
<type 'datetime.time'>
>>> check('integer')
<type 'int'>
>>> check('float')
<type 'float'>
>>> check('numeric')
<class 'decimal.Decimal'>
>>> check('bool')
<type 'bool'>
>>> check('bool_null')
<type 'bool'>

