# Copyright (C) 2009-2010, <PERSON><PERSON> <<EMAIL>>
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program.  If not, see <http://www.gnu.org/licenses/>.


"""Sqlkit printing
--------------------
Each sqlwidget has a ``printing`` attribute that manages printer entries that is an instance
of ``PrintTool``: this can be in one of two different ``styles`` (read more
:ref:`below <appy-vs-ooo>`):

* Oootemplates (prvided by sqlkit library)
* AppyPod

A standard way should be as easy as just setting a directory where templates
should be found and adding menu entries via ``add_menu_entry``. The default
template dir is a subdir of the current directory named ``templates``.

Templates present in this directory -if named as the nick of the sqlwidget-
will be automatically added, but you will normally want to customize the context
or adding variable to it or adding formatting functions (e.g. to transform numbers
into monetary format), that can be easily done connecting to
:ref:`context-ready signal <psignals>`.

The default way of filling the context wraps each object into an :class:`ObjectProxy``
that uses :ref:`fields` to present each value in a human readable format, that means also that
it tries hard to follow foreign keys and substitute values retrieved by :ref:`description`.

Tables: iteration on records
----------------------------

Oootemplate offers a powerfull way to iterate over lines via the ``++`` syntax.
It requires you to define a table's name matching an entry in the context. For
SqlTables the default name for such table is Table1 and all records in the
table end up there.

For SqlMask the default behaviour is to add an entry in the context for
each related attribute (eg: *movies* for the *director*'s mask). Each such entry
holds all the related records as per sqlalchemy class definition. In the director/movie
example the default context would be::

   context = {
      'obj' : <Director: my displayed director>,
      'movies' : [(<Movie: movie1>, <Movie: movie2>)]
   }

It's up to you to prepare the openoffice file to be used as template with a
table named ``movies``: you do that interactively editing oowriter's table attributes.
If you prefere to have to have a different table's name or you need to customize
the content of the record, you simply connet to 'context-ready' signal and prepare the
context directly.

ObjProxy
--------

What stated above is only partially true... each object is wrapped in another object that
allows you to write ``direct_id`` but really returns what you would really want: a human
representation of the id exactly as returned by ``field.lookup_value(id)``.

.. _appy-vs-ooo:
AppyPod vs. OooTemplate
-----------------------

Initially I developed ``oootemplate`` when there where no easy
solutions for printing using OpenOffice files as templates. I like the
sintax that is very powerful. It has many disadvantages thought: it
requires ``uno`` module that is not always easy to obtain. In this
moment ubuntu 14.04/16.04 doesn't provide any more the module for
python2.7. Jessie provides it at the moment.

I later found another package that uses the same idea of an open
document template (be it ``.odt`` or ``.ods``): ``appy.pod``. It has
other advantages:



* it does not require ``uno`` module to produce the open
  document but just to render the ``pdf``

* it copes with images

* it has a way to pass over the rendering of an open document to
  a python3 instance (that's supposed to have ``uno`` module).
  Sqlkit defaults to handling over to /usr/bin/python3 so that
  it normally works in a linux environment. You can overwrite it
  according to your need settings ``PY_PATH`` in
  ``sqlkit.misc.appypod``.



Sqlkit allows to use one or the other, simply set module's variable
:attr:`sqlkit.misc.printing.PRINTING_STYLE` to ``appy`` and you'll be able
to use::

  from sqlkit.misc import printing
  printing.set_printing_style('appy')

``appy.pod`` is part of `appy framework`_
More examples in the demo dir.


API
---

.. autoclass:: PrintTool
   :members: __init__, add_menu_entry, prepare_context, template_dir, server, port, output_dir,
             remote_output_dir, pdf_viewer, odt_viewer, add_to_table, obj_proxy_class

.. autoclass:: ObjProxy
   :members:


.. _`appy framework`: http://appyframework.org/pod.html
"""
import os
import re
import gtk
import urllib
import datetime
import tempfile
import subprocess
from decimal import Decimal

import gobject
from babel import numbers

from sqlkit import _
from sqlkit.widgets.common import dialogs
from sqlkit.misc.utils import get_viewer

MENU_ENTRY = """
  <menubar name="Main">
     <menu action="File">
       <placeholder name="Print-ph">
         <menu action="Print">
           <menuitem action="%s"/>
         </menu>
       </placeholder>
     </menu>
  </menubar>
"""
PRINTING_STYLE = 'ooo'


class PrintAbortException(Exception):
    pass


class DownloadException(Exception):
    pass


class Context(object):

    """
    A context used to render a template. It contains both the data and the way to
    substitute variable with the data.

    """

    translate = None
    """A translation dict. Whenever a variable pattern is found in a
    document, it will be searched for in this dictionary to see if it should
    be translated before sarching in the context.  The goal is to allow
    short variable names in template for narrow cells even if the real
    attribute name in the program is longer. e.g.::

       context.translate['a'] = 'client.user.address'
       context.translate['mq'] = 'minimum_quantity_in_store'

    would allow to write ``$u.city``  instead of ``$client.user.addres.city``
    and ``$mq`` instead of ``$minimum_quantity_in_store``
    """

    def __init__(self, content, lazy=True):
        """
        :param content: a dict with keys the variable names

        :param lazy: boolean. If True (default) invokes
               _implement_lazy_tables to allow the list of objects for a
               table rendered to be directly set as value of the table_name
               entry (rather than a list of lists). See
               :ref:`example` ``Table1`` is lazy ``Table2`` is not.

               The goal is to prevent common errors rather than promoting
               lazy writing. When you only have a single list of objects you
               may easily forget that you may have more than one.

               The assumption is that you don't normally have lists as values
               of context (other that for tables). While probably true, should
               you need lists as contetxt values, you can just set lazy=False.

        """
        self.content = content
        if lazy:
            self._implement_lazy_tables()
        self.translate = {}

    def __getitem__(self, key):

        #import ipdb; ipdb.set_trace()
        return self.content[key]
        # print "Getitem", key, self.content[key]
        # try:
        #     return self.value2string(self.content[key], key)
        # except:

    def __setitem__(self, key, value):
        self.content[key] = value

    def __contains__(self, key):
        return key in self.content

    def _implement_lazy_tables(self):
        """
        implement a lazy context where lists are directly values of
        contents[Table_name] rather then conten[Table_name] = ((...),)
        """
        for key, value in self.content.iteritems():
            if isinstance(value, (list, tuple)) and len(value):
                if not isinstance(value[0], (list, tuple)):
                    self.content[key] = (value, )

    def sub(self, match=None, key=None):
        """
        Substitution used in normal find & replace on the whole document.
        The match must have a group named 'var_name'

        :param match: the match resulting from re.match. if match is None the key
                      is taken from m.group('var_name')
        :param key:   just usefull for debugging purpose.
        """
        if not key:
            assert match is not None
            key = match.group('var_name')

        key = self.translate.get(key, key)

        try:
            value = self.content[key]
            value = unicode(self.value2string(value, key))
            return value
        except KeyError as e:
            return self._deep_sub(None, *key.split('.'))

    def sub_cell(self, match, record_index, table_name, list_num):
        """
        Substitution used for cell values. Specialized version of sub that
        knows how to retrieve data from the object of the cell.

        :param match:         a match that have groups named 'var_name' and 'match'
        :param record_index:  the index of the record in the table's list
        :param table_name:    the openoffice name of the table
        :param list_num:      the odered number of the list in the table (starts from 1).


        """
        assert table_name in self.content, "%s not in context" % self.table_name

        key = match.group('var_name')
        key = self.translate.get(key, key)
        match = match.group('match').replace('$', r'\$')
        try:
            ## the object where we get the values are in a list
            obj = self.content[table_name][list_num][record_index]
            value = unicode(self.value2string(getattr(obj, key), key))

            return unicode(match.sub(value))

        except (KeyError, AttributeError) as e:
            return self._deep_sub(obj, *key.split('.'))

    def _deep_sub(self, obj, key, *tokens):
        """
        search a value in nested structure (eg.: user.name, rs.address.city)
        """
        key = self.translate.get(key, key)
        test_split = key.split('.')
        if len(test_split) > 1:
            return self._deep_sub(obj, *(test_split + list(tokens)))
        try:
            if obj:
                value = getattr(obj, key)
            else:
                value = self.content[key]
        except (KeyError, AttributeError) as e:
            self.missing_keys[key] = None
            return "Missing Key " + key

        for tk in tokens:
            try:
                value = getattr(value, tk)
            except AttributeError as e:
                self.missing_keys[tk] = "%s.%s (%s)" % (key, tk, value)
                value = "No attr %s in %s" % (tk, value)

        return unicode(self.value2string(value, key))

    def _get_records_len(self, table_name, list_num):
        """
        return the number of records that must be rendered for this table
        """
        try:
            return len(self.content[table_name][list_num])
        except KeyError:
            print "No TableName %s in context" % table_name
            return 0

    def value2string(self, value, key):
        """
        :param value: the value found in context
        :param key: the key used to retrieve the value. Note that it is ony
            partially usefull as it can be a key of the context or an attribute
            name of an object containted in the context or in a row

        customize the value from the context.
        You are supposed to customize this method that currently only
        trasforms a date in a locale compliant form (if locale is set)
        """
        ## Don't write 'None' for None...
        if value is None:
            return ''

        if isinstance(value, datetime.date):
            return value.strftime('%x')

        if isinstance(value, (int, Decimal, float)):
            return numbers.format_decimal(value, format="#,###.00")

        return value

    def reset_missing(self):
        """
        reset list of key missing in the context
        """
        self.missing_keys = {}

    def update(self, d):
        """
        Add dict ``d`` to content of this context

        :param d: the dict I want to add to context
        """
        self.content.update(d)

    def __str__(self):
        return "%s" % self.content


class PrintTool(gobject.GObject):

    """
    A print obj that is able to create a default context to handle to
    oootemplate.

    """

    template_dir = None
    """The directory where templates are searched for. Default is cwd/templates.
       It's a path significant for the openoffice server
    """
    local_template_dir = None
    """The directory where templates are searched for locally in case
    OpenOffice server is remote . The only need for this variable is to make
    it possible to :meth:`automagically <__init__>` add templates that show
    up in the menu entry. The templates that get used are clearly on the
    machine where OpenOffice is runnning, it does not need to be visible to
    the client. If defined, will be used just to add meny entry automatically.
    NOTE:: if ``printing_style`` is ``appy``, temmplates are always rendered locally and
    are sent to the server just for the conversion to pdf (or whatever format you need)
    """
    server = '127.0.0.1'
    """the openoffice server to connect to for printing"""

    port = 8100
    """the port to use to connect to the server"""

    output_dir = None
    """the output dir when server is remote as viewed from client (may be an URL)"""

    remote_output_dir = None
    "the output dir when server is remote as viewed from server (a local file for the server)"

    pdf_viewer = get_viewer('pdf')
    """the preferred viewer for pdf files"""

    odt_viewer = get_viewer('odt')
    """the preferred viewer for openoffice templates"""

    server_dir_separator = '/'
    """The server dir separator / (default) or \\: determines how the remote path is built for
    templates and output files. Only used if server is remote (not 127.0.0.1 nor localhost)"""

    proxies = {}
    """the proxies passed to urllib.FancyURLopener (see urllib module documentation)"""

    obj_proxy_class = None
    """The :class:`ObjProxy` to use to wrap objects. Can be changed any time. You can
    customize it so that __getattr__ can return fancy personalization of the attributes"""

    preserve_styles = False
    """Open the template with the ``preserve_styles`` option. It's  mode that allows to have
    a mix of styles in any singl cell. """

    retrieve = True
    """Determine if the file must  be retrieved so as to be opened locally or should be
    be opened remotedly (default True)"""

    __gsignals__ = {
        'context-ready': (
            gobject.SIGNAL_RUN_LAST,
            gobject.TYPE_BOOLEAN,
            # context, template_name, sqlwidget,
            (gobject.TYPE_PYOBJECT, gobject.TYPE_PYOBJECT, gobject.TYPE_PYOBJECT),
        ),
    }

    def __init__(self, master):
        """
        Initializes the printing tool setting a default template dir and checks for
        availability of module 'uno' from openoffice. If uno is missing the printing
        capabilities are made invisible.

        If templates are found with the name ``nick.odt``  where the nick is the nick of
        the sqlwidget, entries are automatically added. This way you can generate templates
        and use them w/o even the need for a single line of code (unless you need to
        customize the context, of course)

        :param master: the table or mask instance of which this is a printing tool
        """
        gobject.GObject.__init__(self)
        self.master = master
        self.template_dir = self.template_dir or os.path.abspath(
            os.path.join(os.curdir, 'templates'))
        self.disabled = False
        self.obj_proxy_class = ObjProxy
        self.look_for_automatic_templates()

    def hide(self):
        "Hide the print meny entries"
        self.master.actiongroup_print.set_visible(False)

    def show(self):
        "Show the print menu entries"
        self.master.actiongroup_print.set_visible(True)
        self.master.actiongroup_print.set_sensitive(True)

    def add_menu_entry(self, description, template_name, server=None, port=None, mode='pdf',
                       output_name=None, accel=None, tip=None, action_name=None):
        """
        Add menu entry with 'description' using 'template'
        Both are fake printers in the sense that are 'printer to file'

        :param description: the description that will appear in the menu entry
        :param template_name: the template name to be used
        :param mode: the mode of the printer: pdf or odt
        :param output_name: the output name. Can be a callable that
              will receive ``template_name`` as parameter. If ``self.output_dir`` is defined
              ``output_name`` will be joined with ``self.ouput_dir`` to retrieve it from the client
        :param accel: accelerator to be used with this
        :param tip: tip for the entry
        :param action_name: action name (defaults to templ\_ + template_name)

        """
        assert template_name is not None, "Template name must be defined"
        output_name = output_name or "%s.%s" % (
            (os.path.splitext(os.path.split(template_name)[1])[0]), mode)

        if not self.disabled:
            self.show()
        dir_name, template_name, template_path = self.split_template_name(template_name)
        if not action_name:
            action_name = 'templ_%s_%s' % (template_name, mode)
        path = '/Main/File/Print-ph/Print/%s' % action_name
        action = self.master.ui_manager.get_action(path)
        if not action:
            self.master.actiongroup_print.add_actions([
                (action_name, None, description, accel, tip, self.use_template,),
            ], (template_path, mode, server, port, output_name))

            self.master.ui_manager.add_ui_from_string(MENU_ENTRY % action_name)

    def split_template_name(self, template_name):
        """
        return a tuple (dir, template_name, template_path)
        if templatename does not contain a path, self.template_dir is used if that leads
        to an existent filename, otherwise it'd be set no None

        :param template_name: a template name possibly with a complete path
        """

        template_path = template_name
        dirname, template_name = os.path.split(template_name)
        if not dirname:
            dirname = self.template_dir
            template_path = os.path.join(dirname, template_name)
            if not os.path.exists(template_path):
                dirname = None

        return dirname, template_name, template_path

    def use_template(self, menuItem=None, template_path=None, mode='pdf',
                     server=None, port=None, output_path=None, preview=False,
                     template_name=None):
        """
        create a context from master and call template

        :param menuItem:   the menu entry that was clicked. Not needed.
        :param tempate_path: the template_path to be used on the server.
        :param mode:  currently ``pdf`` or ``odt``. Default: pdf.
        :param server: the name or ip of the openoffice server. Default: self.server
        :param port:  the port on which the server runs. Default: self.port
        :param output_path: the output name. It can be a callable that will receive 2 args:

                * this PrintTool instance
                * the template_path
        :param preview: (Boolean) if ``True``, the local_output_path is a temporary one and
             the user is not prompted for a position
        :param template_name: template_name. If given the template_path is
        """
        if template_name:
            template_path = self.split_template_name(template_name)[2]
        server = server or self.server
        port = port or self.port

        ## Context
        context = Context({})
        if self.master.is_mask():
            context['obj'] = self.obj_proxy_class(self.master.current, self.master)
        else:
            self.add_to_table(context, 'Table1', self.master.records)

        for rel in self.master.related.keys():
            self.add_to_table(context, rel, self.master.related[rel].records,
                              master=self.master.related[rel])

        self.emit('context-ready', context, os.path.split(template_path)[1], self.master)

        ## Output
        # when the server is remote, the output file is created remotely (remote_output_path)
        # downloaded  and saved (local_output_path) via http, and opened locally

        if callable(output_path):
            output_path = output_path(self, template_path, mode=mode)

        templ = self.get_template(unicode(template_path), context, server=server, port=port)

        if preview:
            f, local_output_path = tempfile.mkstemp(suffix='.pdf')
            if not os.name == 'mac':
                os.close(f)   # under windows if we don't close the file id AdobeReader won't
                              # open the file claiming it's used by others
            remote_output_path = self.get_remote_output_path(
                output_path or 'preview-%s.pdf' % template_name, server)
        else:
            local_output_path = self.get_local_output_path(template_path, mode, output_path)
            remote_output_path = self.get_remote_output_path(output_path, server)

        if not local_output_path:
            return

        if server in ('localhost', '127.0.0.1'):
            if local_output_path == template_path:
                self.master.message(_("Template name (%s) cannot be same as output_path (%s)") % (
                    template_path, output_path), type='ok')
                return
            remote_output_path = local_output_path

        templ.save_as(remote_output_path)
        templ.close()

        ## Show Result
        self.show_result(remote_output_path, local_output_path, server)

    def get_template(self, template_name, context, server=server, port=port):
        """Return a template that have a ``.save_as()`` method
        """
        raise NotImplementedError("you're supposed to implement this")

    def get_remote_output_path(self, output_path, server):
        """
        return the name the remote server must use to save the file
        """

        if self.remote_output_dir and server not in ('localhost', '127.0.0.1'):
            return "%s%s%s" % (self.remote_output_dir, self.server_dir_separator, output_path)
        return output_path

    def show_result(self, remote_output_path, local_output_path, server):
        """
        Open the resulting file with the best possible viewer/editor
        """
        # check that the file exists both if remote...
        if server not in ('localhost', '127.0.0.1') and \
           self.output_dir and re.search('http://', self.output_dir):
            remote_path = "%s/%s" % (self.output_dir, os.path.split(remote_output_path)[1])
            opener = Opener(self.proxies)
            try:
                f = opener.open(remote_path)
            except DownloadException:
                self.master.dialog(text="Missing file %s" % remote_path, type="ok")
                return
            if self.retrieve:
                opener.retrieve(remote_path, local_output_path)
        # or local
        elif not os.path.exists(local_output_path):
            self.master.dialog(text="Missing file '%s'" % local_output_path, type="ok")
            return

        self.open_local_file(local_output_path)

    def debug_missing(self, missing_keys, template, context):
        """
        Communicate that some keys are missing

        :param missing_keys: the dict of missing keys
        :param template: the oootemplate.Template
        :param context: the oootemplate.Context
        """
        msg = _("The following variable are missing in the context")
        # default table name has not been changed and is not present in the .odt file
        # chances are that a localized .odt file names table in a localized way...
        if 'Table1' in context and not 'Table1' in template.tables:
            msg += _("\nA possible error is that I don't see 'Table1' in your template\n"
                     "but I see %s" % " ".join(template.tables))
        for key, where in missing_keys.iteritems():
            if where:
                msg += "\n'%s' (%s)" % (key, where or '')
            else:
                msg += "\n'%s'" % key
        self.master.dialog(text=msg, type='ok')

    def prepare_context(self, context):
        """
        This function is meant to be overridden by a customized one

        :param context: the automatically prepared ``oootemplate.Context``.
               It contains 2 keys:

               * 'obj': the current object for mask and normally *None* for tables
                     (unless an object was edited, in which case it will point to that object)
               * 'Table1' : the list of record currently loaded in this sqlwidget

        You can add any keys but remember to use the correct syntax for tables (a dict
        with lists as <values).

        This is normally used to set totals or arrange so that related table's record
        are used in Tables. Read example 76.

        """
        return context

    def add_to_table(self, context, table_name, obj_list, master=None, reset=True, format=None):
        """
        Add to an openoffice table a list of objects wrapped in ObjProxy

        :param context: the context to be manipulated
        :param table_name: the table_name where objects list must be added
        :param master: the master where to retrieve fields
        :param obj_list: the list of objects to be added
        :param reset: boolean. If True the list becomes the only list, otherwise it's added.
                      Defaults to True
        """

        new_list = [self.obj_proxy_class(o, master or self.master) for o in obj_list]
        if reset:
            context[table_name] = [new_list]
        else:
            context[table_name] += [new_list]

    def get_local_output_path(self, template_path, mode, output_name):
        """
        return the name of the output file on the local machine
        """

        if output_name:
            dir_name, file_name = os.path.split(output_name)
        else:
            dir_name, file_name = os.path.split(template_path)

        base, ext = os.path.splitext(file_name)
        default_filename = "%s.%s" % (output_name and base or base+'-output', mode)

        dialog = PrintDialog(title=_('Print to file'), default_filename=default_filename,
                             run=False)

        #dialog.CURRENT_FOLDER = self.remote_output_dir
        dialog.run()

        filename = dialog.filename
        if filename and not filename.endswith('.%s' % mode):
            filename = '%s.%s' % (filename, mode)
        return filename

    def look_for_automatic_templates(self):
        """
        Look if some templates are available and add entries for them
        """

        if not (os.path.exists(self.template_dir) or (
                self.local_template_dir and os.path.exists(self.local_template_dir))):
            return

        for f in os.listdir(self.local_template_dir or self.template_dir):
            if f == "%s.odt" % self.master.nick:
                self.add_menu_entry(
                    "Print to pdf file", f, mode='pdf', action_name=self.master.nick)
                self.add_menu_entry(
                    "Print to odt file", f, mode='odt', action_name=self.master.nick + 'pdf')

    def open_local_file(self, filename):
        """
        Open file filename with the default application

        :param filename: the name of the file to be opened
        """
        if os.name in ('posix', 'mac'):
            if filename.endswith('pdf'):
                p = subprocess.call((self.pdf_viewer, filename))
            else:
                p = subprocess.call((self.odt_viewer, filename))
        else:
            # I made some test with call(('start', filename)) but in several
            # occasion I coudn't get the start command to find the
            # file. os.startfile() doesn't suffer the same problem.
            os.startfile(filename)


class PrintToolAppy(PrintTool):

    def get_template(self, template_path, context, port=None, server=None):
        import appypod
        return appypod.AppyPodTemplate(template_path, context.content, port=port)


class PrintToolOoo(PrintTool):

    def __init__(self, master):
        super(PrintToolOoo, self).__init__(master)
        try:
            import uno
            self.disabled = False
        except ImportError:
            self.master.sb(_('Disabling printing for lack of "uno" module'), seconds=20)
            self.disabled = True
            self.hide()

    def get_template(self, template_path, context, port=None, server=None):
        import oootemplate as oo

        ## Template & output
        templ = oo.Template(unicode(template_path), server=server, port=port,
                            preserve_styles=self.preserve_styles)
        missing_keys = templ.render(self.prepare_context(context))
        if missing_keys:
            self.debug_missing(missing_keys, templ, context)
        return templ


class PrintDialog(dialogs.SaveDialog):

    FILTER_FILES = (
        (_('Pdf files'), '*.pdf'),
        (_('Odt files'), '*.odt'),
        (_('All files'), '*.*'),
    )

    def current_folder(self):
        return getattr(self, 'CURRENT_FOLDER', None) or dialogs.SaveDialog.current_folder(self)


class ObjProxy(object):

    """
    .. _obj_proxy:

    A proxy that returns a "human value" for each attribute. The default behaviour
    is to use the fields defined for
    the object in the SqlWidget i.e.: field's ``get_human_value``.  You can
    customize ObjProxy changing ``__getattr__`` or simply adding methods whose
    name is ``class_name__attribute_name`` as in::

       class MyObjProxy(ObjProxy):
           def Movie__director_id(self, value):
               return value.title()
       table.printing.obj_proxy_class = MyObjProxy

    in this context value is already the value returned by
    :meth:`sqlkit.fields.Field.get_human_value`
    """

    def __init__(self, obj, master=None):
        if master:
            self.gui_fields = master.gui_fields
        else:
            self.gui_fields = setup_field_validation(obj)

        self.obj = obj

    def __getattr__(self, key):

        value = getattr(self.obj, key)
        if key in self.gui_fields:
            value = self.gui_fields[key].get_human_value(value)

        if hasattr(self, "%s__%s" % (self.obj.__class__.__name__, key)):
            return getattr(self, "%s__%s" % (self.obj.__class__.__name__, key))(value)
        else:
            # at present LoaderProperties don't have a gui_field
            return value


class Opener(urllib.FancyURLopener):

    def http_error_default(self, filename, sock, status, message, response):
        msg = "Error while retrieving file %s.\nStatus code: %s (%s)" % (
            filename, status, message)
        raise DownloadException(msg)


def setup_field_validation(obj):
    """
    Create ``sqlkit.fields.Field`` object: one for each handled field
    """

    from sqlkit.fields import FieldChooser
    from sqlkit.db.minspect import InspectMapper
    from sqlalchemy import orm

    mapper = orm.class_mapper(obj.__class__)

    gui_fields = {}
    info = InspectMapper(mapper)
    field_chooser = FieldChooser(info)

    for field_name in mapper.c.keys():
        Field = field_chooser.get_field(field_name, info.fields[field_name])
        field = Field(field_name, info.fields[field_name])
        gui_fields[field_name] = field

    return gui_fields


def get_print_tool(master, printing_style):
    """Return PrintToolOoo or PrintToolAppy according to settings
    You can set ``sqlkit.misc.printing.PRINTING_STYLE`` to eather
    ``ooo``  or ``appy``, or you can set it when instantiating the
    sqlwidget as ``printing_style`` argument
    """

    pstyle = printing_style or PRINTING_STYLE
    if pstyle == 'ooo':
        return PrintToolOoo(master)
    else:
        return PrintToolAppy(master)


def set_printing_style(style):
    global PRINTING_STYLE
    assert style in ('ooo', 'appy'), _("Printing style must be 'appy' or 'ooo'")
    PRINTING_STYLE = style
