===========
Pyinstaller
===========

Main goals in building this installer are:
 
  * make it easy to test the software and run the demo
  * make it possible to use the command sqledit with an asy installation
    process 

If you just want to use the installer you should simply install it and run
it. If you want to prepare a different installer to add some feature (eg:
more database drivers), you should follow the following steps. Configuration
is inside file pyinstaller-sqledit.spec.

Thanks to <PERSON> -author of pyinstaller- for assisting me in writing
the specs and fixing pyinstaller when needed.

install pyinstaller
===================

install pyinstaller (http://www.pyinstaller.org/) from the last
devel (at least rev 700)::

  svn co http://svn.pyinstaller.org/trunk pyinstaller

and follow instruction in the manual [1]

  Getting Started

      * Installing PyInstaller
      * Building the runtime executables
          just under linux: 
	       sudo apt-get install build-essential python-dev
	       cd source/linux
  	       python Make.py; make
	       cd ../../
      * Configuring your PyInstaller setup


Create the installer
====================

run::

  ./pynstaller/make-installer path_to_Build.py pyinstaller-sqledit.spec

This will create a directory build and a directory pyinstaller. Inside the
latter one you'll find directory sqledit





[1] http://www.pyinstaller.org/static/docs/Manual_v1.1.html
