# -*- mode: python -*-

>>> import os

>>> os.environ['LANG'] = 'en_US.utf8'
>>> import babel
>>> from babel import numbers, support
>>> babel.default_locale()
'en_US'
>>> numbers.default_locale()
'en_US'
>>> numbers.format_number(1.2)
u'1.2'

### it_IT.uft8
>>> os.environ['LANG'] = 'it_IT.utf8'
>>> babel.default_locale()
'it_IT'
>>> numbers.default_locale()
'it_IT'

# This is not the desired behaviour!!!!
>>> numbers.format_number(1.2)
u'1.2'

# This is not eather. So we need to specify the locale all the times.
>>> silent = reload(babel)
>>> silent = reload(numbers)
>>> numbers.format_number(1.2)
u'1.2'
>>> support.format_number(1.2)
u'1.2'
