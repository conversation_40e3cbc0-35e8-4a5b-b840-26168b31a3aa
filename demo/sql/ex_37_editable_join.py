"""mapper & fields/editable join

You can build a join and edit. A simple way is to create the class
is using declarative syntax. In this example you cannot add records but you
can edit any field and use any filters on those fields. The boolean field here is an example of an added column in a pivot table table of a m2m relationship


"""

from sqlkit.widgets.table import Header, ModelProxy

class MyJoin(model.Base):

    __table__ = model.Movie.__table__.join(model.movie_casting)


t = SqlTable(MyJoin, dbproxy=model.db, mode="-i")
t.reload()
