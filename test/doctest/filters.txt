# -*- mode: python -*-

>>> import os
>>> import gtk
>>> os.environ['LANG'] = 'en_US.utf8'

>>> import sys
>>> sys.path.insert(0, '../../demo/sql/model')
>>> sys.path.insert(0, '../../')

>>> from movies import Movie, Director, AllTypes, db
>>> from sqlkit.widgets import SqlMask, SqlTable
>>> from sqlkit.db import defaults
>>> defaults.tables
{}

>>> lay = "title"
>>> sm = SqlMask(Movie, dbproxy=db, layout=lay, naked=True, show=False)
>>> sm.lay_obj.lay
'L=title ae=title:30-'
>>> sm.laygen.fields_in_layout
{'title': 'e=title'}

## completions
>>> sm = SqlMask(Movie, dbproxy=db, naked=True, show=True)
>>> sm.get_value('title')
>>> while gtk.events_pending():
...   a=gtk.main_iteration()    
>>> sm.widgets['fk=director_id'].set_text('f')

## the following will fail with gtk < 2.16 (where a differenfk-entry is defined
>>> sm.completions.director_id.show_possible_completion(sm.widgets['fk=director_id'], 'start', pop=False) 
Faenza
Fellini
>>> sm.completions.director_id.show_possible_completion(sm.widgets['fk=director_id'], 'regexp', pop=False) 
Faenza
Fellini
Truffaut
>>> sm = SqlMask(AllTypes, dbproxy=db, naked=True, show=False)
>>> n = sm.reload()
>>> t = SqlTable(AllTypes, dbproxy=db, naked=True, show=False)

## filters on relationship (have/doesn't have records)
>>> sm = SqlMask(Director, dbproxy=db, naked=True, show=True)
>>> sm.filter_panel.add_filter(movies=True)
>>> sm.reload()
9
>>> sm.filter_panel.clear()
>>> sm.filter_panel.add_filter(movies__null=False)
>>> sm.reload()
9
>>> sm.filter_panel.clear()
>>> sm.filter_panel.add_filter(movies__null=True)
>>> sm.reload()
0


## Filters
>>> sm = SqlMask(AllTypes, dbproxy=db, naked=True, show=False)
>>> sm.add_filter(integer__gte=1)
>>> sm.add_filter(float__gte=1.1)
>>> sm.add_filter(numeric__gte=1.1)
>>> sm.add_filter(date__gte='y-1')
>>> sm.add_filter(date__gte='6/2/08')
>>> sm.add_filter(datetime__null=True)
>>> sm.add_filter(time__null=False)
>>> sm.add_filter(bool=True)
>>> sm.add_filter(bool=False)
>>> sm.add_filter(varchar10__icontains='a')
>>> sm.add_filter(varchar10__ilike='a')
>>> n = sm.reload()
