sqlkit - 0.9.6.1 - Mar, 10 2014

	* fix porting to work for  sqlalchemy < 0.7.8
	* filters: added filter on has/don't have related attributes
	* fields: fixed clean_value of collection
	* image: fixed italian translation + error when saveinga "None" image

sqlkit - 0.9.7 - Not released
	* filters: added possibility to use alternative set of filters by
   	           UNION, EXCEPT and INTERCEPT

sqlkit - 0.9.6 - Jan 2014

	* porting: works with sqlalchemy 0.8
	* images: fix a bug that made some images disappear on resize
	          ATTENTION: adds a backword incompatibility. If you have 
	          inherited from ImageField, you need to add arg ``new``
	          in the signature
	* dialogs: now Esc works as Cancel
	* unicode: fixed unicode conversion from db
	* sqledit: improved import so that it won't crash python2.7 in
	           corner cases ('layout' imported from sqledit conf)
	* filters: added a syntax to refer to (e.g.) working days after a
	           certain date
	*          Added 'D' as alias for 'end of today'
	* oootemplate: added possibility to embed images (partial implementation)
	
sqlkit - 0.9.5.1 - Feb 23 2012

	* filters: + added shortcut Control-Alt-f to add a filter and reload
 	             starting from the field widget
	           + added "operator" empty or NULL to catch both filter
	             in one go
	* layout: minor fix in setting TextView. It was only noticeable
	           with some themes
	* images:  fix: the image was never deleted
	* templates: now libreoffice is searched for as well if ooffice is missing


sqlkit - 0.9.5 - Feb 7 2012

        * porting to SQLA 0.7+
	* table:
             +  if treestore is used, row deletions of a parent  implies
                deletion of children
	     +  on image field, no entry to cshow image if the image is
                missing
             + added a tooltip for the image name
        * docs: minor changes
        * fields: enum now only suggest empty if null is allowed
                  fixes in image rendering and fix in path
        * sqledit: fix in option parsing (Opt -T)
	* filter_panel: it's now possible to get rid of filters already added
	* minor fixes

sqlkit - 0.9.4 - Apr 11 2011
	* printing: This release adds support for printing from sqlkit
             	using OpenOffice templates that in turn uses 'uno' module provided
	        from the OpenOffice project and a custom system that allows to
	        write report templates w/o any programming know-how.

		This system called oootemplates, can be used from any Python program
		independently from the rest of sqlkit.

sqlkit - ******* - Mar 31 2011

	* demo: - now the schema is more standard and can be fed to other db
 	          backend (but postgresql requires TRUE/FALSE and not 1/0)
		- the db is created using tempdir
	* mask: default layout now automatically sqitches to multicolumn
	        if table has many fields
	* types: work around to Bytea fields that where not present in
	        0.5.*
	* completion: fix the case in which a search_field is defined in
	        _sqlkit_field. previously this rased an error on
	        completion.
	* totals: fix: pressing 't' on the table on a non -numeric field
	        must not try to generate a total
	* setup: now creates sqledit suitable for each OS (Mainly now
	        windows has its own one, the others where already ok)
	* sqledit: added option --create-tables, to create table after
	        reading the models

sqlkit - 0.9.3.1 - Mar 11 2011

	* localization: added german localization
	* mask: fix in set_records() when pk option was used and no record
	        found
	* fields: enum field clean_value should not validate NOT NULL

sqlkit - 0.9.3 - Mar 3 2011

	* table/mask: added possibility to duplicate a record
        * fields: added
	        * images as different render options of a varchar field in
	          model column definition
                * enum as  varchar field as render option of varchar or
	          integer field
		* now fields liste to changes in the model and react
	          possibly changing the widget, this is an important step
	          toward a better MVC support
	* completion: added possibility to add completion to fields that
	        * normally don't have it (e.g.: numbers)
		* fix dynamic completion also in enum mode
		* fixed an invalidation error of the fey key
        * hooks: * on_change_value now requires one more arg and is issued als
	           for masks when the value is changed (not when initially set)
                 * generally more consistent and tested hooks
        * package: fix in the package that adds demo 
        * docs: * completely new home page, with slider of features
                * updated docs on how to install as now all is much easier with
                  pygtk-all-in-one installer
                * improved documentation on widgets and fields
	* dependancies: removed dependancy on glade!!! Now gtk.Builder is used
        * widgets: 
                 * fixed a useless dialog that prompted to save when not
                   needed
		 * improvement on some error messages
		 * added signal record-new
        * table: added api:
                 * added edited_row_model_path
		 * get_selected_row()
		 * get_obj_at_path()
		 * hide_filed(field_name)
		 * added possibility to add a row as child of another (when
	           TreeStore is choosen)
	* totals: 
                 * now method sum has model, path and iter also to be able to
	           build special totals of just some hierarchical level
		 * added signal 'computed' when the sum is computed
	* miniwidgets: added signal set-value
	* layout: 
	        * now date widget does not expand anymore in useless
	          mode. defaults to 10 chars
		* added tooltip do date popup to sy how to abort (Esc!)
		* fk_key now uses an icon in the widget rather that an arrow
	          separately
        * demo: * added option -i that drops you in an interactive ipython
	          shell if you have it installed
	* filters: added possibility to filter a foreign key on it's real
	          value rather than default behaviour of remote value
        * table browsers: fixed table reflection that was not working ny more

sqlkit - ******* - Feb 1 2011
	setup: setup tuning in MANIFEST that didn't add demo and tests
	
sqlkit - ******* - Jan 27 2011
	translations: added polish translation
	docs:  removal of javacript that I'm not sure can redistribute

	
sqlkit - 0.9.2 -December 18 2010
	setup.py: modified so that it should be easier to install by using
   	          simply 'python setup.py install'. Uses distribute_setup.py
	          if needed to install distribute
	demo:     now the model is recreated each time the demo is started
	debian:   removed debian staff: Pietro Battiston started the
	          debian packaging as a standalone project
	
sqlkit -0.9.1 - Spetember 29 2010

	usability: added tooltips in many menu entries

	backends: full support for firebird. _sqlkit_table is called sqlkit_table
          for that backend

	hooks: minor modification to 'on_activate' hook for table widget so that
	       it behaves as 'on_activate' for masks

	fields: * added std_cleaup als as classmethod, usefull for integers and for
                  some corner cases
                * lookup now raises a warning rather then an errore when a lookup
                  returns multiple values

        sqledit: * minor improvement in the way fkey are represneted when
                   introspecting a table
	         * added sqledit customization via nick subdirectories of
                   .sqledit. and possibility to use very simple addon

         table:   * minor fix to RecordInMask

	 defaults: improved docmentation on hook "on_completion"

	 docs: improved documentation on completion, fields and validation

	 validation: delayed till the save dialog pops the dfferences widget so that
		     it's only triggered if needed

	 dateedit: minor fix. DateEdit widget didn't set data from a handwritten
		   entry in some circumstancies

sqlkit 0.9 - September 8 2010

	* support: fixes to sqlalchemy 0.6 (now my current reference)
	* tests:   fixed some tests that didn't pass under python2.6
	* orphan:  changed the way orphan are handled, now an explicit
	           parameter must be passed
	* layout:  changed the way xml is read to bypass an odd segfault on
	           accented letters
	* layout:  fixed completely broken demo snippets
	* table:   added possibility to hide some demos
	           improved look of headers under std ubuntu theme
	* totals:  minor fix
	* filter_panel: improved interaction (Ctrl-l, Ctrl-o, Ctrl-r)
	* filters: added negation of regexp for postgresql
	* mask:    added a method to quickly add labels to frame
	           (.set_frame_label)
	* docs:    improved look and added section "advanced"
	* completion: minor improvement: now you can add 2 conditions on
	           the same field in some occasions
	* sqledit: added icons for main toolbar actions
	* tooltips: added tooltip to some actions

	
sqlkit 0.9.rc1 - August 7 2010

	* support:   * support for sqlalchemy 0.6 has been added
	
	* sqlwidget: * syntax change: now the first argument is not a keywork. It's
	             the table_name/table/class/mapper. keyword args table, mapper
	             class_ are now deprecated
		   * improved handling of ForeignKey when the model has a backref that
	             prevents orphans
	           * added django-like syntax to .reload()
		   * mapper: now the mapper can have a function
		   * get_title()
		   * now decimal from the keypad works nicely (inserts "." or ","
	             according to locale)
		   * fixed 'delete-event' callback: self.current needed to be
	             cleaned and session-extensions needed to be cleaned to prevent
	             several calls to hooks on commit/flush
		   * now NumLock is ignored when using hot keys (Control-n, Tab...)
	             was totally misleading
	           * fixed 'set_mode'
		   * added documentation on using UIManager proficiently
		   * format: keywork format is handled over to related table too, as
	             for col_width.
		   * added hook 'on_pre_layout' to be able to set a mapping fields in hooks
		   * now any char can be used for 'title' as is process by
	             gobject.markup_escape_text
		   * refresh after save. Now after each save a refresh is done so
	             that any db-computed field is correctly displayed. This is
	             propagated to the possibly existing parent widgets.
	
	* filters:   * fixed filtering an integer with value 0 (previously was
	             ignored)
		   * filter for boolean: added IS NOT TRUE/ IS NOT FALSE
		   * selection of widgets now reflects widget displayed
		   * the filter_panel is now a view, you can highly customize the
	             appearence, creating new fields or displaying fields from the
	             record. Methods add_column and replace_column help you
	             customize it. The possiblity to have grouped lines in the
	             filter panel (as in a 2 level tree) is now supported. You can
	             eve have totals in the filter panel
		   * added notnull filter (tat was already documented)
	
	
	* completion: * added possibility to add attributes to completion result. That
	              opens new possibility to on_completion hook
	
	* fields:    * internal important restructure. 
		   * now imported from sqlkit.fields
		   * fixed import loop. No gtk is implied when importing sqlkit.fields
	 	   * Now attributes nullable and editable are
	             propertyies and affect the widget, if any
		   * now fields in a widget can be calculated from other fields. you
		     can add a field just inheriting a sqlkit.field.Field and
		     overwriting cleaned_value(). 
		     A field that is not persisted will be turned into read-only and
		     will not be validated on saving. 
		   * now you can pass a mapping that forces which field will be
		     used, a signal has been added to be able to do that in Hooks.
		   * slightly changed behaviour of BLANK_OK in fields. Now no change
		     is proposed on fields that are not edited.
		     
	* table:   * views have been added. The same model can now  be represented in
	           differet TreeViews displaying possibly different columns. Views
	           have the selection sincronized so that get_selected_obj() point
	           to the same object.
	           (Note that 'button-press-event' call back now needs 
	            one more arg, the view).
	         * added possibility to sort locally (w/o hitting the
	           database). This also adds the possibility to sort related tables.
	         * Control-x is now an alias for Control-k
		 * Improved RecordInMask (minor fixes). Added Control-m to start it.
		 * FKeyRecord now can use a custom layout/nick
		 * fix in get_value()
		 * better edit experience (no more vanishing text if not saving)
		 * several minor fixes 
		 * totals: added possibility to hide the grand-total
		 * totals: added the local sort tat is faster as it's done in the
	           treeview, w/o fetching from the database.
		 * Total and sorting in tables works for not persisted fields as
	           well.
	         * subtotal can be toggled using the letter 'b' on a focused
	           treeview (record...)
		 * export is now different for each view
		 * record_in_mask now checks for usaved record in session
		 * record_in_mask/fkey_record_in_mask can be called programmatically
		 * hooks: added on_activate that was not implemented for tables
		 * self.current now points to the currently edited OR SELECTED
	           record. previously was only on currently edited on None. This
	           makes it much more usable.
	
	* demo:    * Fixed demo (opt -g)
	          
	
	* tests:   * added some tests
	
	* mask:    * added signal 'pre-display'
		 * fixed BooleanNullRenderer that occasionally failed to set
	   	   correctly the values
		 * layout: fixed alignment problem that prevented from being able to
		   set entry length
	
	
	
	
sqlkit 0.8.7 - December 21 2009

	* docs: general improvement:
	           - started backword incompatibilities
                   - fix errors and missing parts on hooks
                   - completion: fixex and integration
	           
	* completion: improved warning messaged on lookup
	              lookup values that use search_filter as in '%(field_name)s', will now use
	              DictLike to prevent empty field_name values to be
	              filled with ugly looking 'None'

	* filters:   * django syntax now has also 'notlike', 'inotlike' and 'notin'
	             * dates now accept also date object
	             * added reasonable message when filter values cannot
	               be converted to usable values
	
	* diff dialog: 
	           - improved representation of differences
	           - now it's possible to open the dialog from menu (not
 	             only when saving)

	* using generic selectable:
	           - fixed sorting with joined selectables
	
	* portability: 
	           - minor fixes to be used on MacOS!
	           - diminished warnings when used with gtk 2.16
	           - forced cast to unicode to prevent some mistakes in
                     menus with diacritics under windows.
	
	* usability:
	           - column header now hilight to show it's sensitive
	           - RecordInMask and FKeyMask now use different icons 
	           - zoom-fit for tables is bound to Control-z
                   - sqledit has now --limit option
	           - a right click in a ForeignKey in a Mask allows to edit the
                	foreign Mask (as was already possible with Table)
	           - filter panel's output treeview selection now follow
             	     represented object in mask
	           - completion improved: when text matches possible
                     solution no entry-completion is used and value is accepted
	
	* internals: 
	           - a modelproxy has been added to allow both liststores
	             and treestores
	           - rationalized get_selected_obj() and get_current_obj()
                     and self.current

	* signals & hooks:  * added record-saved
                            * fixes in 'on_field_name_validation' when
	                    field is fk
	
	* defaults: fixed several bugs in default module, added tests

	* sqlwidget: introduced a new and easy way to set the mode of a
                     sqlwidget: mode can be iudb (INSERT, UPDATE, DELETE,
	             BROWSE and more)

	* tables:   Now Table can show content as hierarchy on objects
	
sqlkit ******* - September 29 2009

	* translation: added spanish translation

	* datetools: added @2m syntax and added Y-1m capability
	
sqlkit 0.8.6 - September 10 2009

	* package: 
	          - improved setup.py: install_requires is now handled by
  	            the script rather than by setuptools since often
	            setuptools fails detecting already present packages
	          - debian package now depends on psycopg & mysql also
	          - added binary version via pyinstaller
	
sqlkit 0.8.6 rc5- August 6 2009

	* sqledit: 
	         - gui improved
	         - possibility to edit directly the configuration tables
	         - improved the editing mode of the configuration
	           tables via completion
	         - added toggle of casting of blank fields to NULL
	         - improved documentation
	         - added dialog to interactively write backend uri: it may 
	           attempt connection immediately
	         - added possibility to run the demo from the starting
	           dialog
	         - added possibility to count records in a table
	         - modified to be used inside pyinstaller
	         - added workaround when LANG is not defined
	         - bug fixes: sometime was not possible to add new records
	         - now delete-orpahn is correctly handles at least in
	           simple cases
	
	* completion: 
	         - added dynamic filters for Foreign Key
	         - added set_values 
                 - fix fixed group_by
	         - fixed completion + postgres inn start mode (was always
	           regexp)
	         - corrected markup excape for completion
	         - fix autocompletion on SimpleCompletion
	
	* sqlwidget: 
	         - now resize accepts also -1 to let the present value
	         - improved has_changed for FKey field and Collection (m2m)
	         - added post-commit signal
	         - deleting a record without having the privileges now 
	           correctly handles the (not) deletion of the row from
	           the gtk table. Similarly when adding a record.
	         - differences dialog: db NULL values will show up as None
	           (rather than '')
	         - order_by: improved, now several columns can be
	           addressed, and will be translated in column object
	           behind the scene to ensure good behaviour even in
	           joins.
	
	* table: 
	         - now clicking on a non selected table wwill not toggle a
	           boolean field. Everithing behaves better...
	         - fix in totals. Subtotal by year raise exception if missing
	         - fix in the way a new record is detected in m2m
	         - added possibility to set the layout nick for foreign
	           key columns; this is the nick of the registered layout 
	           we want to use in a mask used to edit a foreign key
	           starting from the menu of the cell
	         - improved cell validation. Two more bugs fixed
	         - fixed the delete record function that removed the
	           object even when eletion was aborted by backend
	         - fixed case in m2m relation in which no more that one
	           row could be added
	
	* filters: a wrong relative-date string will pop a dialog box
  	           rather than an unhadled exception

	* db.utils: - added get_fields
 	           - fixed dateutils that evaluated TODAY at import time
	           - added get_create_statement for single tables
	           - added undocumented sqlkit.misc.conf with reader of the 
	             configuration (.sqledit)
	
	* many bug fixes:
	         - typo in completion that prevented filters
	         - improved parsing in timedelta ugly widget
	         - label maps now works as expected
	         
sqlkit 0.8.6-rc4 - 

	* completion: deep rewrite that enable many fetures: 
	         * now completion is configurable directly with sqlalchemy
	           syntax also and is more clear what you expect.
	         * conditions can be set on relation also
	         * a bunch of tests show exactly the expected behaviour
	         * some more examples have been added
	         * added possibility to have 2 completion behaviuor on the
	           same field
	         * completion now works also on fkey of an m2m

	* filters: 
	         * added '@' syntax for sqlkit.misc.datetools
	         * filters now work for expressions too. See example
	           ex_30.
	
	* signals: * added 'changed_context' signal for tables to make it
	             easy to follow changes in RecordInMask/RecordMaskFkey

	* hooks: added after_flush_postexec hook

	* sqlwidget: 
	         * DifferencesDialog (to show which differences need to
	           be saved) was improved

	* table:  Added default menu entry to edit the ForeignKey field
           	  when clicking on a cell that holds a ForeignKey.
	          This feature is called RecordMaskFkey

	* utils:  Added register_class/get_class to store/restrieve
	          classes. Needed by RecordMaskFKey 

	* miniwidgets: fixed nullable boolean widget

	* postgres: added handling of pg_interval
	
sqlkit 0.8.6-rc3 - 

	* hooks: - hooks are now in a separated class that can be registered
    	           globally under a nickname. If present 'default' hook
                   is used
 	         - added after_flush and on_init

	* layout: now layout can be registered globally in the same way
	           hooks can.

	* validation: - added ValidationWarnings that allow to warn the
	           user but don't abort saving operation
	          - fix cases in which validation didn't allow to save 
	* fixes: after_flush signal

	* fields: get_human value returs a formatted value (date/numbers)
	
	* mask: fix tips. In some cases accented letters would crash
	        right cick on label pops info on field

	* mysql: fixed reflection of some Types and REGEXP operator

	* export: now ForeignKey are translated on exporting

	* table: better with of columns
	         some Types are mapped to Varchar and handled (eg. PGInet)
	         Modify menu allows to re-show columns that have been
  	           hidden interactively
	         added function select_path

sqlkit 0.8.6-rc2 - March, 26 2009

	* sqlwidget: record_has_changed forces setting attributes in the
	             object: this makes validation more stable 
	             save dialogs: much better representation of
	             differences that are to be saved
	             readonly mode now correctly sets actiongroup_edit 
	               not sensitive
	
	* uimanager: menus are now handled with uimanager so that was possible
	             to make them sensitive o context
	
	* signals:  added signal after-flush (added demo)
	            added delete-event to sqlwidget
	            'button-press-event'now has also a 'menu' arg
	
	* filters: filters on relationships are now aliased (see note in
	           docs on relationship)
	
	* hooks:   hooks have been added: 
		     + on_completion__%s
		     + on_change_value__%s
		     + on_validation
		     + on_field_validation__%s
		     + on_activate__%s
	           Added 5 examples in demo
	
	* table:   added popup menu on right click on records to:
	             + delete
	             + view in mask (see below)
	             + add new record
	          popup menu is changable in a simple way from
                 	'button-press-event' signal (see demo)
	          better handling of silent deletion of object not changed
	          added View Record that can also show with a layout (see demo)
	
	* miniwidgets: rewritten DateEdit to warn and complein of parse errors

	* django2sa: now join are made in a way that is accepted by sa rel 0.5+

	* utils: fixed get_differences to return all modified attributes 
	         and added get_history

	* validation: no validation on empty values (if not nullable is
            	triggered just before saving)
	
	* fixes: as usual many fixes:
	         - back the possibility to delete foreign keys in table
	         - completion in m2m corner cases
	         - fixed hide_fields()

	* fields: float now have format=None
	          you can now set BLANK_OK globally if instead of setting
  	             it on all fields
	
	* demo: many more examples and added not nullable fields and set
	        cascade on relation between director and movies.
	
sqlkit 0.8.6-rc1 - March, 10 2009
	
	* table: * complete restructure of TreeColumn/CellRenderers creation
	           now will be easyer to customize them
	         * fix in height in netsed table definition (e.g.:m2m=field:3)
	         * great improve in dialog on saveing/deleteing records
	           now is possible to see changes
	         * added signal 'press-button-event'
	         * improved detection of useless save dialog
	         * added Contrl-x shortcut as Contr-k
	         * fix in deleting when in m2m mode: obj was not expunjed
	         * date cell renderer now is a normal varchar. t loast
                   calendar but can be deleted!!!
	         * order_by: now column obj are used that make it possible
	           to use it also when join constraints are used
	* gui_fields: now any loop on fields is done on gui_fields, that
	        can be mapped in the class (persisted) or not. irst step
	        to adding normal, not presisted, fields. No longer vfield
	        list. 
	* validation:
		this is the main improvement of this release. Any check
        	that is known by the fields is done before committing AND
        	all errors are dispayed in a single dialog
	
		In table: now table correctly handles errors in
		validation: it's not possible to leave a field with wrong
		value. Nullable values are checkd only when committing
	* mask: * added undo menu entry (trivial,triggers discard_changes)
	        * layoutgenerator; change in syntax. Now TXS=description
	          will correctly add label while TXS==description will not
	
	* MultiLine: fix for multiline
	* input: now keypd work correctly and handles KP_Decimal according
 	        to locale, when in interger/decimal or float fields
	* fields: * fixed default format for floats. Now #.###.@. Before it
       		    truncated decimals
	          * added get_human_value()
	* defaults: it's now possible to have defaults local to an
        	instance of sqlwidget
	* sqledit: improved GUI(now open correct N. of lines _ C-l)
	* completion: added 'enum' mode to mimic enum widget with doubleclick
        	several bug-fixes
	* debug: get_widget_signals now returns signals for all __bases__
	* layout: stock imaes can be used as in i=gtk-save
	* layout: added MenuItem that accepts StockItem and Text.
	* defaults: now local defaults act as global defaults (eg: fk method)
	* sqlwidget: title can be set in any moment
	
sqlkit ******* - 23.1.09
	* fix release:
	   + filter resulted not working
	   + sqlite demo database was corrupted
	   + completion fixes
	* added labels for explanation of operators
	* uses clean_value to validate input from filters, so that float
 	   and dates are correctly parsed
	* improved completion behaviour: when a match is selected the
	   editing finishes
	* table columns: added an informative label
	* improved browsing of data: now boolean and date/datetime do not
	   enter in edit mode directly
	* status bar: added a "seconds" argument indicating how many
	   seconds a message should stay in the status bar
	* widgets: resize() uses idle_add() so does not interfere width
	   algorithm that decides optimal width
	
sqlkit 0.8.5 - 16.1.09	
	
	* localization: added localization for numbers and dates
	                added localization for messages
	                -- now waiting for translations  ;-)  --
	* table: - added a smart way to automatically set dimentions of the
	           table based on info retrieved from the database
	           (thanks to Pietro Battiston)
	         - complete rewrite of the column setup function
	         - added a basic multiline cellRenderer
	         - grately improved the navigation with Tab that now
	           correctly triggers validation/completion
	* fields: many improvements in clean_value/validation
	* mask: - added handling of comments
	        - fixed integer/float miniwiget (0 was rendered as  '')
	        - text now uses gtk.WRAP_WORD
	        - fixed current_idx was a class attribute not an instance attr
	* layout: added check and hints on errors for Panes widgets
	* django_syntax: fix in typo
	* filters: fixed handlung of boolena & NULL check in related tables
	* signals: - record_selected -> record-selected to follow gtk standard
	           - added records-displayed
	* widgets/layout: added label_map support
	* sqlwidget: rationalized menu entries
	
sqlkit 0.8.4 - 20.12.08

	* totals: fix in date when date is missing
	* completion: many fixes
	              group_by beahaviour now works correctly
	              .filter works also for fkey fields
	* postgres: fixed PGChar handling
	* sqledit: implemented 'copy' feature in .sqleditdb
	* table: added sort DESC entry in each column
                 added export in .csv format of a table
	         added entry to hide/show columns
		 set_field_list: now columns are rearranged to reflect
              	 list order
	* speed: many fixes to speed up browsing of data when working with
	         a remote db:
                   + session: 'expire_on_commit=False' as default of dbProxy
	           + table: fixed caching of values
	* debug: fixed a situation in which printing debug caused an error
	         added a function that prints metadata.create_all() statement
	* django_syntax: cleanup and creation of tests
	* filter_panel: added 'tree' attribute that determines if records
  	         in output page must be rendered as tree
	* icons: fixex bug that prevented to show some icons in Windows
	* mask: added set_records (as table has)
	* demo: more demo examples
	        added button to pop debug
	* join: introduced join between table (tables="table1 table2" not
    	        yet documented)
	* m2m: introduced 'set_editable' attribute (feature still in progress)
	
	
sqlkit 0.8.3.3 - 22.11.08

	* import statement - complete restructure: 
	   1. now SqlMask and SqlTable are imported from sqlkit.widgets.
	   2. dbg now works for gtk debug as well (was broken after
   	      unifyinng debug , layout and sqlkit package)
	* DbProxy.get_session returns a session with autoflush=False
	* unsaved_changes_exist: now uses
	   session.is_modified(passive=True) to prevent flushing if the
	   session has autoflush=True. Needs sqlalchemy after 5312
	* m2m: now m2m uses a separate completion so that fields can not be
	   edited but onnly picked up.
	   Now adding a record does not save, to prevent flushing a
	   (possibly) not yet completed field in master SqlMask
	* completion: uses self.format field to show the possible
	   completions, that can be set in _sqlkit_table or via sqlkit.db.utils
	* table: added method set_opts
	* defaults: improved (but far from optimal) handling of defaults:
	   not callable default set in sqlalchemy are used.
	* fixes: many minor fixes
	
sqlkit 0.8.3.2 - 12.11.08
	
	* fixed sqlite reflection for SLDate & SLDateTime

sqlkit 0.8.3 - 10.11.08

	* Initial Release.

